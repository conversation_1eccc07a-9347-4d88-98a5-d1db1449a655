"""
Optimalizovaný ML pipeline pro Ball Bearing Predictive Challenge 2025
Kombinuje nejlepší přístupy pro diskrétní trénovací data a spojitou predikci.
"""

import numpy as np
import pandas as pd
import pickle
import joblib
from scipy import signal, stats
from scipy.interpolate import UnivariateSpline
from scipy.signal import hilbert
import pywt

from sklearn.model_selection import train_test_split, KFold
from sklearn.preprocessing import RobustScaler
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import Matern, WhiteKernel, RBF
from sklearn.isotonic import IsotonicRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error

import xgboost as xgb
import lightgbm as lgb

import warnings
warnings.filterwarnings('ignore')


class AcousticEmissionFeatureExtractor:
    """Optimalizovaný extraktor příznaků pro AE signály."""
    
    def __init__(self, fs=1.5625e6):
        self.fs = fs
        self.feature_names = []
        
    def extract_critical_ae_features(self, signal_data):
        """Extrahuje kritické AE parametry identifikované ve výzkumu."""
        features = {}
        
        # 1. RMS - přímá korelace se zatížením
        features['rms'] = np.sqrt(np.mean(signal_data**2))
        
        # 2. Špičková amplituda - maximální napěťové události
        features['peak_amplitude'] = np.max(np.abs(signal_data))
        
        # 3. Hit rate - počet AE událostí
        threshold = 3 * np.std(signal_data)
        peaks, _ = signal.find_peaks(np.abs(signal_data), height=threshold)
        features['hit_rate'] = len(peaks) / (len(signal_data) / self.fs)
        
        # 4. Energetický obsah
        features['energy'] = np.sum(signal_data**2)
        features['log_energy'] = np.log(features['energy'] + 1e-10)
        
        # 5. Spektrální příznaky závislé na zatížení
        freqs, psd = signal.welch(signal_data, fs=self.fs, nperseg=512)
        
        # Energie v pásmech kritických pro ložiska
        bands = [(0, 10e3), (10e3, 50e3), (50e3, 100e3), (100e3, 200e3), (200e3, 500e3)]
        for i, (low, high) in enumerate(bands):
            mask = (freqs >= low) & (freqs < high)
            features[f'band_energy_{i}'] = np.sum(psd[mask])
        
        # 6. Crest factor - indikátor impulsního charakteru
        features['crest_factor'] = features['peak_amplitude'] / features['rms'] if features['rms'] > 0 else 0
        
        # 7. Spektrální centroid
        if np.sum(psd) > 0:
            features['spectral_centroid'] = np.sum(freqs * psd) / np.sum(psd)
        else:
            features['spectral_centroid'] = 0
            
        return features
    
    def extract_emd_features(self, signal_data):
        """Empirical Mode Decomposition pro časově-frekvenční analýzu."""
        try:
            from PyEMD import EMD
            emd = EMD()
            IMFs = emd(signal_data)
            
            features = {}
            for i, imf in enumerate(IMFs[:3]):  # První 3 IMF
                features[f'imf_{i}_energy'] = np.sum(imf**2)
                features[f'imf_{i}_freq'] = self._dominant_frequency(imf)
            return features
        except:
            # Fallback na wavelet dekompozici
            return self._wavelet_fallback(signal_data)
    
    def _wavelet_fallback(self, signal_data):
        """Waveletová dekompozice jako náhrada EMD."""
        features = {}
        coeffs = pywt.wavedec(signal_data, 'db4', level=3)
        for i, c in enumerate(coeffs):
            features[f'wavelet_{i}_energy'] = np.sum(c**2)
        return features
    
    def _dominant_frequency(self, signal_data):
        """Vypočítá dominantní frekvenci signálu."""
        freqs = np.fft.fftfreq(len(signal_data), 1/self.fs)
        fft = np.fft.fft(signal_data)
        idx = np.argmax(np.abs(fft[:len(fft)//2]))
        return abs(freqs[idx])
    
    def extract_all_features(self, multi_channel_data):
        """Extrahuje všechny příznaky ze všech kanálů."""
        all_features = []
        
        for ch in range(multi_channel_data.shape[0]):
            channel_data = multi_channel_data[ch]
            
            # Kritické AE příznaky
            ae_features = self.extract_critical_ae_features(channel_data)
            for name, value in ae_features.items():
                all_features.append(value)
            
            # EMD/Wavelet příznaky
            decomp_features = self.extract_emd_features(channel_data)
            for name, value in decomp_features.items():
                all_features.append(value)
        
        # Mezikanálové příznaky
        # Koherence mezi kanály
        for i in range(multi_channel_data.shape[0]):
            for j in range(i+1, multi_channel_data.shape[0]):
                f, Cxy = signal.coherence(multi_channel_data[i], multi_channel_data[j], 
                                        fs=self.fs, nperseg=256)
                all_features.append(np.mean(Cxy))
        
        return np.array(all_features)


class SmoteRRegressor:
    """Implementace SMOTE pro regresi s diskrétními úrovněmi."""
    
    def __init__(self, k_neighbors=5, augment_factor=2):
        self.k_neighbors = k_neighbors
        self.augment_factor = augment_factor
        self.discrete_levels = np.array([2500, 7500, 12500, 17500])
        
    def fit_resample(self, X, y):
        """Vytvoří syntetické vzorky mezi diskrétními úrovněmi."""
        X_aug = [X]
        y_aug = [y]
        
        # Pro každou sousední dvojici úrovní
        for i in range(len(self.discrete_levels) - 1):
            level1, level2 = self.discrete_levels[i], self.discrete_levels[i+1]
            
            # Najdi vzorky pro tyto úrovně
            idx1 = np.where(y == level1)[0]
            idx2 = np.where(y == level2)[0]
            
            if len(idx1) > 0 and len(idx2) > 0:
                n_synthetic = min(len(idx1), len(idx2)) // self.augment_factor
                
                for _ in range(n_synthetic):
                    # Náhodně vyber vzorky
                    sample1_idx = np.random.choice(idx1)
                    sample2_idx = np.random.choice(idx2)
                    
                    # Lineární interpolace s náhodným faktorem
                    alpha = np.random.uniform(0.2, 0.8)
                    
                    # Interpoluj features
                    synthetic_x = (1 - alpha) * X[sample1_idx] + alpha * X[sample2_idx]
                    
                    # Interpoluj target s mírným šumem
                    synthetic_y = (1 - alpha) * level1 + alpha * level2
                    synthetic_y += np.random.normal(0, 50)  # ±50g šum
                    
                    X_aug.append(synthetic_x.reshape(1, -1))
                    y_aug.append([synthetic_y])
        
        return np.vstack(X_aug), np.concatenate(y_aug)


class OptimizedBearingPredictor:
    """Optimalizovaný prediktor kombinující nejlepší metody."""
    
    def __init__(self):
        self.feature_extractor = AcousticEmissionFeatureExtractor()
        self.scaler = RobustScaler()
        self.smoter = SmoteRRegressor(k_neighbors=5, augment_factor=3)
        
        # Ensemble modelů
        self.models = {
            'gpr': GaussianProcessRegressor(
                kernel=Matern(length_scale=1.0, nu=1.5) + WhiteKernel(noise_level=1),
                alpha=1e-6,
                normalize_y=True,
                n_restarts_optimizer=3
            ),
            'xgb': xgb.XGBRegressor(
                n_estimators=300,
                learning_rate=0.05,
                max_depth=8,
                subsample=0.8,
                colsample_bytree=0.8,
                gamma=0.1,
                reg_alpha=0.1,
                reg_lambda=1.0,
                random_state=42
            ),
            'lgb': lgb.LGBMRegressor(
                n_estimators=300,
                learning_rate=0.05,
                num_leaves=50,
                max_depth=-1,
                min_child_samples=5,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=1.0,
                random_state=42,
                verbose=-1
            ),
            'rf': RandomForestRegressor(
                n_estimators=200,
                max_depth=None,
                min_samples_split=2,
                min_samples_leaf=1,
                max_features='sqrt',
                random_state=42,
                n_jobs=-1
            )
        }
        
        # Isotonic regression pro post-processing
        self.isotonic = IsotonicRegression(out_of_bounds='clip')
        
        # Váhy pro ensemble (budou optimalizovány)
        self.ensemble_weights = {'gpr': 0.3, 'xgb': 0.3, 'lgb': 0.3, 'rf': 0.1}
        
    def extract_features(self, data):
        """Extrahuje optimalizované příznaky."""
        print(f"Extrakce příznaků pro {len(data)} vzorků...")
        features = []
        
        for i, sample in enumerate(data):
            if i % 500 == 0:
                print(f"  Zpracováno {i}/{len(data)} vzorků...")
            features.append(self.feature_extractor.extract_all_features(sample))
        
        return np.array(features)
    
    def train(self, X_train, y_train):
        """Trénuje ensemble s data augmentation."""
        print("\n1. Data augmentation pomocí SmoteR...")
        X_aug, y_aug = self.smoter.fit_resample(X_train, y_train)
        print(f"   Původní vzorky: {len(X_train)}, Augmentované: {len(X_aug)}")
        
        print("\n2. Normalizace příznaků...")
        X_aug_scaled = self.scaler.fit_transform(X_aug)
        X_train_scaled = self.scaler.transform(X_train)
        
        print("\n3. Trénování ensemble modelů...")
        trained_models = {}
        val_scores = {}
        
        # Rozdělení pro validaci vah
        X_tr, X_val, y_tr, y_val = train_test_split(
            X_aug_scaled, y_aug, test_size=0.2, random_state=42
        )
        
        for name, model in self.models.items():
            print(f"   Trénuji {name}...")
            
            if name == 'xgb':
                # XGBoost s early stopping
                model.fit(
                    X_tr, y_tr,
                    eval_set=[(X_val, y_val)],
                    verbose=False
                )
            else:
                model.fit(X_tr, y_tr)
            
            # Validační skóre
            val_pred = model.predict(X_val)
            val_mae = mean_absolute_error(y_val, val_pred)
            val_scores[name] = val_mae
            print(f"     Validační MAE: {val_mae:.2f}g")
            
            trained_models[name] = model
        
        # Optimalizace vah na základě validačního výkonu
        total_score = sum(1/score for score in val_scores.values())
        self.ensemble_weights = {
            name: (1/score)/total_score for name, score in val_scores.items()
        }
        print(f"\n   Optimalizované váhy: {self.ensemble_weights}")
        
        # Přetrénování na všech datech
        print("\n4. Finální trénování na všech augmentovaných datech...")
        for name, model in self.models.items():
            if name == 'xgb':
                model = xgb.XGBRegressor(**model.get_params())
            elif name == 'lgb':
                model = lgb.LGBMRegressor(**model.get_params())
            elif name == 'rf':
                model = RandomForestRegressor(**model.get_params())
            else:  # GPR
                model = GaussianProcessRegressor(**model.get_params())
            
            model.fit(X_aug_scaled, y_aug)
            self.models[name] = model
        
        # Isotonic regression pro smoothing
        print("\n5. Kalibrace isotonic regression...")
        ensemble_pred = self._ensemble_predict(X_train_scaled)
        self.isotonic.fit(ensemble_pred, y_train)
        
        return self
    
    def _ensemble_predict(self, X):
        """Vážený ensemble predict."""
        predictions = []
        
        for name, model in self.models.items():
            pred = model.predict(X)
            weighted_pred = pred * self.ensemble_weights[name]
            predictions.append(weighted_pred)
        
        return np.sum(predictions, axis=0)
    
    def predict(self, X):
        """Predikce s post-processing."""
        # Normalizace
        X_scaled = self.scaler.transform(X)
        
        # Ensemble predikce
        ensemble_pred = self._ensemble_predict(X_scaled)
        
        # Isotonic smoothing
        smoothed_pred = self.isotonic.predict(ensemble_pred)
        
        # Finální clipping
        final_pred = np.clip(smoothed_pred, 0, 19200)
        
        return final_pred
    
    def predict_with_uncertainty(self, X):
        """Predikce s kvantifikací nejistoty pomocí GPR."""
        X_scaled = self.scaler.transform(X)
        
        # GPR predikce s nejistotou
        gpr_pred, gpr_std = self.models['gpr'].predict(X_scaled, return_std=True)
        
        # Ensemble predikce
        ensemble_pred = self._ensemble_predict(X_scaled)
        
        # Isotonic smoothing
        smoothed_pred = self.isotonic.predict(ensemble_pred)
        
        # Finální predikce
        final_pred = np.clip(smoothed_pred, 0, 19200)
        
        return final_pred, gpr_std


def main():
    print("="*70)
    print("OPTIMALIZOVANÝ PIPELINE PRO BALL BEARING CHALLENGE 2025")
    print("="*70)
    
    # Načtení dat
    print("\nNačítání dat...")
    with open('../AMSM_LOZISKA_2025/student_data.pkl', 'rb') as f:
        data = pickle.load(f)
    
    train_data = data['train_dataset']
    train_responses = data['train_response']
    test_data = data['test_dataset']
    
    print(f"Trénovací data: {train_data.shape}")
    print(f"Testovací data: {test_data.shape}")
    print(f"Diskrétní úrovně zatížení: {sorted(set(train_responses))}")
    
    # Inicializace prediktoru
    predictor = OptimizedBearingPredictor()
    
    # Extrakce příznaků
    print("\nExtrakce optimalizovaných AE příznaků...")
    X_train = predictor.extract_features(train_data)
    X_test = predictor.extract_features(test_data)
    
    print(f"\nPočet příznaků: {X_train.shape[1]}")
    
    # Uložení extrahovaných příznaků
    np.save('X_train_optimized.npy', X_train)
    np.save('X_test_optimized.npy', X_test)
    
    # Trénování
    predictor.train(X_train, train_responses)
    
    # Predikce
    print("\n6. Vytváření finálních predikcí...")
    predictions = predictor.predict(X_test)
    
    # Analýza predikcí
    print("\n7. Analýza predikcí...")
    print(f"   Rozsah predikcí: {predictions.min():.2f} - {predictions.max():.2f}g")
    print(f"   Průměr: {predictions.mean():.2f}g")
    print(f"   Std: {predictions.std():.2f}g")
    
    # Histogram predikcí
    train_levels = [2500, 7500, 12500, 17500]
    print("\n   Distribuce predikcí kolem trénovacích úrovní:")
    for level in train_levels:
        close_preds = np.sum(np.abs(predictions - level) < 500)
        print(f"   ±500g od {level}g: {close_preds} predikcí")
    
    # Uložení
    print("\n8. Ukládání výsledků...")
    np.save('predictions_optimized.npy', predictions)
    joblib.dump(predictor, 'optimized_bearing_predictor.pkl')
    
    print("\n" + "="*70)
    print("PIPELINE DOKONČEN!")
    print("="*70)
    print("Predikce uloženy do: predictions_optimized.npy")
    print("Model uložen do: optimized_bearing_predictor.pkl")
    
    # Predikce s nejistotou pro ukázku
    print("\nUkázka predikcí s nejistotou (prvních 10 vzorků):")
    pred_subset, std_subset = predictor.predict_with_uncertainty(X_test[:10])
    for i in range(10):
        print(f"  Vzorek {i+1}: {pred_subset[i]:.2f}g ± {std_subset[i]:.2f}g")


if __name__ == "__main__":
    main()