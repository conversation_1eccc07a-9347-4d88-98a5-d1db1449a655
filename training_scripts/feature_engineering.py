import numpy as np
import pandas as pd
from scipy import signal, stats
from scipy.fft import fft, fftfreq
import pywt
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class FeatureExtractor:
    """Třída pro extrakci příznaků z akustických signálů."""
    
    def __init__(self, fs: float = 1.5625e6):
        self.fs = fs
        self.feature_names = []
        
    def extract_time_domain_features(self, signal_data: np.ndarray) -> Dict[str, float]:
        """Extrahuje příznaky v časové doméně."""
        features = {}
        
        # Základní statistiky
        features['mean'] = np.mean(signal_data)
        features['std'] = np.std(signal_data)
        features['var'] = np.var(signal_data)
        features['min'] = np.min(signal_data)
        features['max'] = np.max(signal_data)
        features['median'] = np.median(signal_data)
        features['q25'] = np.percentile(signal_data, 25)
        features['q75'] = np.percentile(signal_data, 75)
        features['iqr'] = features['q75'] - features['q25']
        
        # Vyšší momenty
        features['skew'] = stats.skew(signal_data)
        features['kurtosis'] = stats.kurtosis(signal_data)
        
        # Vibrační příznaky
        features['rms'] = np.sqrt(np.mean(signal_data**2))
        features['abs_mean'] = np.mean(np.abs(signal_data))
        features['peak'] = np.max(np.abs(signal_data))
        features['peak_to_peak'] = features['max'] - features['min']
        
        # Faktory
        if features['rms'] > 0:
            features['crest_factor'] = features['peak'] / features['rms']
            features['shape_factor'] = features['rms'] / features['abs_mean']
        else:
            features['crest_factor'] = 0
            features['shape_factor'] = 0
            
        if features['abs_mean'] > 0:
            features['impulse_factor'] = features['peak'] / features['abs_mean']
        else:
            features['impulse_factor'] = 0
        
        # Zero crossing rate
        zero_crossings = np.where(np.diff(np.sign(signal_data)))[0]
        features['zero_crossing_rate'] = len(zero_crossings) / len(signal_data)
        
        # Peak counting (různé prahy)
        thresholds = [0.01, 0.02, 0.03, 0.05]
        for thr in thresholds:
            peaks, _ = signal.find_peaks(np.abs(signal_data), height=thr)
            features[f'peak_count_{thr}'] = len(peaks)
        
        # Energy features
        features['energy'] = np.sum(signal_data**2)
        features['log_energy'] = np.log(features['energy'] + 1e-10)
        
        # Temporal features
        features['abs_diff_mean'] = np.mean(np.abs(np.diff(signal_data)))
        features['abs_diff_std'] = np.std(np.abs(np.diff(signal_data)))
        
        return features
    
    def extract_frequency_domain_features(self, signal_data: np.ndarray) -> Dict[str, float]:
        """Extrahuje příznaky ve frekvenční doméně."""
        features = {}
        
        # FFT
        yf = fft(signal_data)
        xf = fftfreq(len(signal_data), 1/self.fs)[:len(signal_data)//2]
        spectrum = 2.0/len(signal_data) * np.abs(yf[:len(signal_data)//2])
        
        # Spektrální statistiky
        features['spectral_mean'] = np.mean(spectrum)
        features['spectral_std'] = np.std(spectrum)
        features['spectral_max'] = np.max(spectrum)
        
        # Dominantní frekvence
        dom_idx = np.argmax(spectrum)
        features['dominant_freq'] = xf[dom_idx]
        features['dominant_freq_amplitude'] = spectrum[dom_idx]
        
        # Top N frekvencí
        top_indices = np.argsort(spectrum)[-5:][::-1]
        for i, idx in enumerate(top_indices):
            features[f'top_freq_{i+1}'] = xf[idx]
            features[f'top_freq_{i+1}_amp'] = spectrum[idx]
        
        # Spektrální momenty
        magnitude = np.abs(spectrum)
        if np.sum(magnitude) > 0:
            features['spectral_centroid'] = np.sum(xf * magnitude) / np.sum(magnitude)
            
            # Spektrální rozptyl
            features['spectral_spread'] = np.sqrt(
                np.sum(((xf - features['spectral_centroid'])**2) * magnitude) / np.sum(magnitude)
            )
            
            # Spektrální šikmost a špičatost
            if features['spectral_spread'] > 0:
                features['spectral_skewness'] = (
                    np.sum(((xf - features['spectral_centroid'])**3) * magnitude) / 
                    (np.sum(magnitude) * features['spectral_spread']**3)
                )
                features['spectral_kurtosis'] = (
                    np.sum(((xf - features['spectral_centroid'])**4) * magnitude) / 
                    (np.sum(magnitude) * features['spectral_spread']**4)
                )
            else:
                features['spectral_skewness'] = 0
                features['spectral_kurtosis'] = 0
        else:
            features['spectral_centroid'] = 0
            features['spectral_spread'] = 0
            features['spectral_skewness'] = 0
            features['spectral_kurtosis'] = 0
        
        # Energie v pásmech (kritické na základě EDA)
        bands = [
            (0, 5000, '0_5khz'),
            (5000, 10000, '5_10khz'),
            (10000, 20000, '10_20khz'),
            (20000, 30000, '20_30khz'),
            (30000, 50000, '30_50khz'),
            (50000, 75000, '50_75khz'),
            (75000, 100000, '75_100khz'),
            (100000, 150000, '100_150khz'),
            (150000, 200000, '150_200khz'),
            (200000, 300000, '200_300khz'),
            (300000, 500000, '300_500khz')
        ]
        
        total_energy = np.sum(spectrum**2)
        for low, high, name in bands:
            mask = (xf >= low) & (xf < high)
            band_energy = np.sum(spectrum[mask]**2)
            features[f'energy_{name}'] = band_energy
            if total_energy > 0:
                features[f'energy_ratio_{name}'] = band_energy / total_energy
            else:
                features[f'energy_ratio_{name}'] = 0
        
        # Spektrální entropie
        if np.sum(spectrum) > 0:
            normalized_spectrum = spectrum / np.sum(spectrum)
            spectral_entropy = -np.sum(normalized_spectrum * np.log(normalized_spectrum + 1e-10))
            features['spectral_entropy'] = spectral_entropy
        else:
            features['spectral_entropy'] = 0
        
        # Spektrální roll-off
        cumsum = np.cumsum(spectrum)
        if cumsum[-1] > 0:
            rolloff_idx = np.where(cumsum >= 0.85 * cumsum[-1])[0][0]
            features['spectral_rolloff'] = xf[rolloff_idx]
        else:
            features['spectral_rolloff'] = 0
        
        return features
    
    def extract_wavelet_features(self, signal_data: np.ndarray, wavelet: str = 'db4', levels: int = 5) -> Dict[str, float]:
        """Extrahuje příznaky pomocí waveletové transformace."""
        features = {}
        
        # Waveletová dekompozice
        coeffs = pywt.wavedec(signal_data, wavelet, level=levels)
        
        # Pro každou úroveň
        for i, coeff in enumerate(coeffs):
            level_name = f'wavelet_level_{i}'
            
            # Statistiky koeficientů
            features[f'{level_name}_mean'] = np.mean(coeff)
            features[f'{level_name}_std'] = np.std(coeff)
            features[f'{level_name}_energy'] = np.sum(coeff**2)
            features[f'{level_name}_entropy'] = -np.sum(coeff**2 * np.log(coeff**2 + 1e-10))
            features[f'{level_name}_max'] = np.max(np.abs(coeff))
            
        # Wavelet packet decomposition pro detailnější analýzu
        wp = pywt.WaveletPacket(data=signal_data, wavelet=wavelet, maxlevel=3)
        
        # Energie v různých wavelet packet nodes
        for node in wp.get_level(3, 'natural'):
            features[f'wp_energy_{node.path}'] = np.sum(node.data**2)
        
        return features
    
    def extract_channel_correlation_features(self, multi_channel_data: np.ndarray) -> Dict[str, float]:
        """Extrahuje příznaky založené na korelaci mezi kanály."""
        features = {}
        
        n_channels = multi_channel_data.shape[0]
        
        # Korelační matice
        corr_matrix = np.corrcoef(multi_channel_data)
        
        # Extrahuj horní trojúhelník (bez diagonály)
        upper_triangle = corr_matrix[np.triu_indices(n_channels, k=1)]
        
        features['corr_mean'] = np.mean(upper_triangle)
        features['corr_std'] = np.std(upper_triangle)
        features['corr_min'] = np.min(upper_triangle)
        features['corr_max'] = np.max(upper_triangle)
        
        # Cross-correlation features
        for i in range(n_channels):
            for j in range(i+1, n_channels):
                # Maximální cross-korelace
                xcorr = np.correlate(multi_channel_data[i], multi_channel_data[j], mode='same')
                features[f'xcorr_max_{i}_{j}'] = np.max(np.abs(xcorr))
                
                # Lag při maximální korelaci
                max_idx = np.argmax(np.abs(xcorr))
                features[f'xcorr_lag_{i}_{j}'] = max_idx - len(xcorr)//2
        
        # Koherence mezi kanály (průměr)
        coherence_sum = 0
        count = 0
        for i in range(n_channels):
            for j in range(i+1, n_channels):
                f, Cxy = signal.coherence(multi_channel_data[i], multi_channel_data[j], 
                                        fs=self.fs, nperseg=256)
                coherence_sum += np.mean(Cxy)
                count += 1
        
        features['avg_coherence'] = coherence_sum / count if count > 0 else 0
        
        return features
    
    def extract_all_features(self, sample: np.ndarray) -> np.ndarray:
        """Extrahuje všechny příznaky z vícekanálového vzorku."""
        all_features = []
        feature_names = []
        
        # Pro každý kanál
        for ch in range(sample.shape[0]):
            channel_data = sample[ch]
            
            # Časová doména
            time_features = self.extract_time_domain_features(channel_data)
            for name, value in time_features.items():
                all_features.append(value)
                feature_names.append(f'ch{ch}_{name}')
            
            # Frekvenční doména
            freq_features = self.extract_frequency_domain_features(channel_data)
            for name, value in freq_features.items():
                all_features.append(value)
                feature_names.append(f'ch{ch}_{name}')
            
            # Wavelet příznaky
            wavelet_features = self.extract_wavelet_features(channel_data)
            for name, value in wavelet_features.items():
                all_features.append(value)
                feature_names.append(f'ch{ch}_{name}')
        
        # Příznaky založené na vztazích mezi kanály
        correlation_features = self.extract_channel_correlation_features(sample)
        for name, value in correlation_features.items():
            all_features.append(value)
            feature_names.append(name)
        
        # Statistiky napříč kanály
        channel_means = [np.mean(sample[ch]) for ch in range(sample.shape[0])]
        channel_stds = [np.std(sample[ch]) for ch in range(sample.shape[0])]
        channel_rmss = [np.sqrt(np.mean(sample[ch]**2)) for ch in range(sample.shape[0])]
        
        all_features.extend([
            np.mean(channel_means),
            np.std(channel_means),
            np.mean(channel_stds),
            np.std(channel_stds),
            np.mean(channel_rmss),
            np.std(channel_rmss)
        ])
        
        feature_names.extend([
            'cross_channel_mean_mean',
            'cross_channel_mean_std',
            'cross_channel_std_mean',
            'cross_channel_std_std',
            'cross_channel_rms_mean',
            'cross_channel_rms_std'
        ])
        
        # Ulož názvy příznaků při prvním volání
        if not self.feature_names:
            self.feature_names = feature_names
        
        return np.array(all_features)
    
    def transform(self, data: np.ndarray) -> np.ndarray:
        """Transformuje celý dataset na příznaky."""
        n_samples = data.shape[0]
        features_list = []
        
        print(f"Extrakce příznaků pro {n_samples} vzorků...")
        
        for i in range(n_samples):
            if i % 100 == 0:
                print(f"  Zpracováno {i}/{n_samples} vzorků...")
            
            features = self.extract_all_features(data[i])
            features_list.append(features)
        
        print(f"Extrakce dokončena. Celkem {len(self.feature_names)} příznaků.")
        
        return np.array(features_list)
    
    def get_feature_names(self) -> List[str]:
        """Vrátí názvy všech extrahovaných příznaků."""
        return self.feature_names

def test_feature_extraction():
    """Testuje extrakci příznaků na malém vzorku dat."""
    import pickle
    
    # Načti data
    with open('AMSM_LOZISKA_2025/student_data.pkl', 'rb') as f:
        data = pickle.load(f)
    
    train_data = data['train_dataset']
    train_responses = data['train_response']
    
    # Vytvoř extraktor
    extractor = FeatureExtractor()
    
    # Test na prvních 10 vzorcích
    test_features = extractor.transform(train_data[:10])
    
    print(f"\nTvar výsledných příznaků: {test_features.shape}")
    print(f"Počet příznaků: {len(extractor.get_feature_names())}")
    print(f"\nPrvních 20 názvů příznaků:")
    for i, name in enumerate(extractor.get_feature_names()[:20]):
        print(f"  {i+1}. {name}")
    
    # Uložení extraktoru pro pozdější použití
    import joblib
    joblib.dump(extractor, 'feature_extractor.pkl')
    print("\nExtraktor uložen do 'feature_extractor.pkl'")

if __name__ == "__main__":
    test_feature_extraction()