# Waveletový ML pipeline - standalone verze

import numpy as np
import pandas as pd
import pickle
import joblib
import pywt
from sklearn.model_selection import train_test_split, KFold, cross_val_score
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error
import xgboost as xgb
import lightgbm as lgb
from scipy import signal, stats
from scipy.signal import hilbert
import matplotlib.pyplot as plt
import time
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

class WaveletFeatureExtractor:
    """Pokročilá extrakce příznaků pomocí waveletové transformace pro akustické signály."""
    
    def __init__(self, fs: float = 1.5625e6):
        self.fs = fs
        self.feature_names = []
        
        # Wavelety vhodné pro akustické signály
        self.wavelets = {
            'db4': 'Daubechies 4',  # <PERSON><PERSON><PERSON><PERSON> pro ostré přechody
            'sym5': 'Symlet 5',     # <PERSON><PERSON><PERSON><PERSON><PERSON>, vhodný pro vibrace
            'coif3': 'Coiflet 3',   # <PERSON><PERSON><PERSON><PERSON>, dobrý pro kontinuální signály
            'dmey': 'Discrete Meyer' # Výborný pro frekvenční lokalizaci
        }
        
    def cwt_features(self, signal_data: np.ndarray, wavelet: str = 'morl') -> Dict[str, float]:
        """Extrahuje příznaky pomocí spojité waveletové transformace (CWT)."""
        features = {}
        
        # Definuj škály pro CWT (odpovídají frekvencím)
        # Pro Morletův wavelet: frekvence ≈ (center_freq * fs) / scale
        # Center freq pro Morlet je ~0.8125
        scales = np.arange(1, 128)  # Pokrývá široký frekvenční rozsah
        
        # CWT
        coefficients, frequencies = pywt.cwt(signal_data, scales, wavelet, 1/self.fs)
        
        # Energie v různých frekvenčních pásmech
        freq_bands = [
            (1000, 10000, 'low'),
            (10000, 50000, 'mid_low'),
            (50000, 100000, 'mid'),
            (100000, 200000, 'mid_high'),
            (200000, 500000, 'high')
        ]
        
        for low_f, high_f, band_name in freq_bands:
            band_mask = (frequencies >= low_f) & (frequencies <= high_f)
            if np.any(band_mask):
                band_coeffs = coefficients[band_mask, :]
                
                # Energie v pásmu
                features[f'cwt_energy_{band_name}'] = np.sum(np.abs(band_coeffs)**2)
                
                # Maximální koeficient v pásmu
                features[f'cwt_max_{band_name}'] = np.max(np.abs(band_coeffs))
                
                # Časová variabilita energie v pásmu
                energy_over_time = np.sum(np.abs(band_coeffs)**2, axis=0)
                features[f'cwt_energy_std_{band_name}'] = np.std(energy_over_time)
                features[f'cwt_energy_kurt_{band_name}'] = stats.kurtosis(energy_over_time)
        
        # Scalogram statistiky
        scalogram = np.abs(coefficients)**2
        
        # Ridge detection - najdi dominantní frekvence v čase
        ridge_indices = np.argmax(scalogram, axis=0)
        ridge_frequencies = frequencies[ridge_indices]
        
        features['cwt_ridge_freq_mean'] = np.mean(ridge_frequencies)
        features['cwt_ridge_freq_std'] = np.std(ridge_frequencies)
        features['cwt_ridge_freq_change'] = np.mean(np.abs(np.diff(ridge_frequencies)))
        
        return features
    
    def dwt_packet_features(self, signal_data: np.ndarray, wavelet: str = 'db4', level: int = 5) -> Dict[str, float]:
        """Extrahuje příznaky pomocí wavelet packet decomposition."""
        features = {}
        
        # Wavelet packet decomposition
        wp = pywt.WaveletPacket(data=signal_data, wavelet=wavelet, maxlevel=level)
        
        # Pro každý uzel na nejnižší úrovni
        for node in wp.get_level(level, 'freq'):
            node_data = node.data
            if len(node_data) > 0:
                # Energie uzlu
                energy = np.sum(node_data**2)
                features[f'wp_{wavelet}_node_{node.path}_energy'] = energy
                
                # Entropie uzlu
                if energy > 0:
                    entropy = -np.sum((node_data**2/energy) * np.log(node_data**2/energy + 1e-10))
                    features[f'wp_{wavelet}_node_{node.path}_entropy'] = entropy
                
                # Statistiky koeficientů
                features[f'wp_{wavelet}_node_{node.path}_std'] = np.std(node_data)
                features[f'wp_{wavelet}_node_{node.path}_kurt'] = stats.kurtosis(node_data)
                
        # Relativní energie distribution
        total_energy = sum([v for k, v in features.items() if 'energy' in k])
        if total_energy > 0:
            for key in list(features.keys()):
                if 'energy' in key:
                    features[key + '_rel'] = features[key] / total_energy
        
        return features
    
    def stationary_wavelet_features(self, signal_data: np.ndarray, wavelet: str = 'db4', level: int = 5) -> Dict[str, float]:
        """Extrahuje příznaky pomocí stacionární waveletové transformace (SWT)."""
        features = {}
        
        # Ujisti se, že délka signálu je mocnina 2 pro SWT
        n = len(signal_data)
        next_pow2 = int(2**np.ceil(np.log2(n)))
        if n < next_pow2:
            # Pad signál nulami
            signal_padded = np.pad(signal_data, (0, next_pow2 - n), mode='constant')
        else:
            signal_padded = signal_data[:next_pow2]
        
        # SWT
        try:
            coeffs = pywt.swt(signal_padded, wavelet, level=level, trim_approx=False)
            
            for i, (cA, cD) in enumerate(coeffs):
                level_name = f'swt_{wavelet}_level_{i+1}'
                
                # Aproximační koeficienty
                features[f'{level_name}_approx_energy'] = np.sum(cA**2)
                features[f'{level_name}_approx_std'] = np.std(cA)
                features[f'{level_name}_approx_entropy'] = -np.sum(cA**2 * np.log(np.abs(cA) + 1e-10))
                
                # Detailní koeficienty
                features[f'{level_name}_detail_energy'] = np.sum(cD**2)
                features[f'{level_name}_detail_std'] = np.std(cD)
                features[f'{level_name}_detail_kurt'] = stats.kurtosis(cD)
                
                # Poměr energií
                if features[f'{level_name}_approx_energy'] > 0:
                    features[f'{level_name}_energy_ratio'] = (
                        features[f'{level_name}_detail_energy'] / 
                        features[f'{level_name}_approx_energy']
                    )
        except:
            # Pokud SWT selže, použij DWT jako zálohu
            pass
        
        return features
    
    def time_frequency_features(self, signal_data: np.ndarray) -> Dict[str, float]:
        """Extrahuje časově-frekvenční příznaky."""
        features = {}
        
        # Hilbertova transformace pro okamžitou frekvenci a amplitudu
        analytic_signal = hilbert(signal_data)
        amplitude_envelope = np.abs(analytic_signal)
        instantaneous_phase = np.unwrap(np.angle(analytic_signal))
        instantaneous_frequency = np.diff(instantaneous_phase) / (2.0 * np.pi) * self.fs
        
        # Statistiky okamžité frekvence
        features['inst_freq_mean'] = np.mean(instantaneous_frequency)
        features['inst_freq_std'] = np.std(instantaneous_frequency)
        features['inst_freq_max'] = np.max(instantaneous_frequency)
        features['inst_freq_change_rate'] = np.mean(np.abs(np.diff(instantaneous_frequency)))
        
        # Statistiky amplitudové obálky
        features['envelope_mean'] = np.mean(amplitude_envelope)
        features['envelope_std'] = np.std(amplitude_envelope)
        features['envelope_crest'] = np.max(amplitude_envelope) / np.mean(amplitude_envelope)
        
        # Detekce burst událostí (náhlé zvýšení energie)
        envelope_threshold = np.mean(amplitude_envelope) + 2 * np.std(amplitude_envelope)
        burst_indices = amplitude_envelope > envelope_threshold
        features['burst_count'] = np.sum(np.diff(burst_indices.astype(int)) == 1)
        features['burst_duration_ratio'] = np.sum(burst_indices) / len(burst_indices)
        
        return features
    
    def multi_resolution_features(self, signal_data: np.ndarray) -> Dict[str, float]:
        """Kombinuje různé waveletové transformace pro multi-resolution analýzu."""
        features = {}
        
        # Pro každý typ waveletu
        for wavelet_name in ['db4', 'sym5', 'coif3']:
            # DWT features
            coeffs = pywt.wavedec(signal_data, wavelet_name, level=5)
            
            # Relativní energie na každé úrovni
            energies = [np.sum(c**2) for c in coeffs]
            total_energy = sum(energies)
            
            if total_energy > 0:
                for i, energy in enumerate(energies):
                    features[f'mra_{wavelet_name}_level_{i}_rel_energy'] = energy / total_energy
            
            # Cross-level korelace
            for i in range(len(coeffs)-1):
                if len(coeffs[i]) > 0 and len(coeffs[i+1]) > 0:
                    # Resample na stejnou délku pro korelaci
                    min_len = min(len(coeffs[i]), len(coeffs[i+1]))
                    corr = np.corrcoef(coeffs[i][:min_len], coeffs[i+1][:min_len])[0, 1]
                    features[f'mra_{wavelet_name}_corr_level_{i}_{i+1}'] = corr
        
        return features
    
    def extract_all_wavelet_features(self, sample: np.ndarray) -> np.ndarray:
        """Extrahuje všechny waveletové příznaky z vícekanálového vzorku."""
        all_features = []
        feature_names = []
        
        # Pro každý kanál
        for ch in range(sample.shape[0]):
            channel_data = sample[ch]
            
            # CWT příznaky
            cwt_features = self.cwt_features(channel_data)
            for name, value in cwt_features.items():
                all_features.append(value)
                feature_names.append(f'ch{ch}_{name}')
            
            # DWT packet příznaky
            for wavelet in ['db4', 'sym5']:
                dwt_features = self.dwt_packet_features(channel_data, wavelet, level=4)
                for name, value in dwt_features.items():
                    all_features.append(value)
                    feature_names.append(f'ch{ch}_{name}')
            
            # SWT příznaky
            swt_features = self.stationary_wavelet_features(channel_data, 'db4', level=4)
            for name, value in swt_features.items():
                all_features.append(value)
                feature_names.append(f'ch{ch}_{name}')
            
            # Časově-frekvenční příznaky
            tf_features = self.time_frequency_features(channel_data)
            for name, value in tf_features.items():
                all_features.append(value)
                feature_names.append(f'ch{ch}_{name}')
            
            # Multi-resolution příznaky
            mr_features = self.multi_resolution_features(channel_data)
            for name, value in mr_features.items():
                all_features.append(value)
                feature_names.append(f'ch{ch}_{name}')
        
        # Cross-channel wavelet koherence
        for i in range(sample.shape[0]):
            for j in range(i+1, sample.shape[0]):
                # Wavelet koherence na specifické úrovni
                coeffs_i = pywt.wavedec(sample[i], 'db4', level=3)
                coeffs_j = pywt.wavedec(sample[j], 'db4', level=3)
                
                for level in range(len(coeffs_i)):
                    if len(coeffs_i[level]) > 0 and len(coeffs_j[level]) > 0:
                        min_len = min(len(coeffs_i[level]), len(coeffs_j[level]))
                        corr = np.corrcoef(coeffs_i[level][:min_len], 
                                         coeffs_j[level][:min_len])[0, 1]
                        all_features.append(corr)
                        feature_names.append(f'wavelet_corr_ch{i}_ch{j}_level{level}')
        
        # Ulož názvy příznaků při prvním volání
        if not self.feature_names:
            self.feature_names = feature_names
        
        return np.array(all_features)
    
    def transform(self, data: np.ndarray) -> np.ndarray:
        """Transformuje celý dataset na waveletové příznaky."""
        n_samples = data.shape[0]
        features_list = []
        
        print(f"Extrakce waveletových příznaků pro {n_samples} vzorků...")
        
        for i in range(n_samples):
            if i % 100 == 0:
                print(f"  Zpracováno {i}/{n_samples} vzorků...")
            
            features = self.extract_all_wavelet_features(data[i])
            features_list.append(features)
        
        print(f"Extrakce dokončena. Celkem {len(self.feature_names)} příznaků.")
        
        return np.array(features_list)

class WaveletMLPipeline:
    """Pipeline s waveletovými příznaky pro predikci zatížení."""
    
    def __init__(self):
        self.feature_extractor = WaveletFeatureExtractor()
        self.scaler = None
        self.best_model = None
        
    def run_pipeline(self, data_path: str):
        """Spustí celý pipeline s waveletovými příznaky."""
        
        # Načti data
        print("Načítání dat...")
        with open(data_path, 'rb') as f:
            data = pickle.load(f)
        
        train_data = data['train_dataset']
        train_responses = data['train_response']
        test_data = data['test_dataset']
        
        print(f"Trénovací data: {train_data.shape}")
        print(f"Testovací data: {test_data.shape}")
        
        # Extrakce waveletových příznaků
        X_train = self.feature_extractor.transform(train_data)
        X_test = self.feature_extractor.transform(test_data)
        
        # Uložení extrahovaných příznaků
        np.save('X_train_wavelet.npy', X_train)
        np.save('X_test_wavelet.npy', X_test)
        
        print(f"\nPočet waveletových příznaků: {X_train.shape[1]}")
        
        # Normalizace
        print("\nNormalizace příznaků...")
        self.scaler = RobustScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Nahraď NaN hodnoty
        X_train_scaled = np.nan_to_num(X_train_scaled, nan=0.0, posinf=0.0, neginf=0.0)
        X_test_scaled = np.nan_to_num(X_test_scaled, nan=0.0, posinf=0.0, neginf=0.0)
        
        # Rozdělení na train/val
        X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
            X_train_scaled, train_responses, test_size=0.2, random_state=42
        )
        
        # Trénování modelů
        results = {}
        
        # 1. Random Forest s waveletovými příznaky
        print("\n" + "="*60)
        print("RANDOM FOREST S WAVELETOVÝMI PŘÍZNAKY")
        print("="*60)
        
        rf_model = RandomForestRegressor(
            n_estimators=200,
            max_depth=20,
            min_samples_split=5,
            min_samples_leaf=2,
            max_features='sqrt',
            random_state=42,
            n_jobs=-1
        )
        
        start_time = time.time()
        rf_model.fit(X_train_split, y_train_split)
        train_time = time.time() - start_time
        
        y_pred_val = rf_model.predict(X_val_split)
        val_mae = mean_absolute_error(y_val_split, y_pred_val)
        
        print(f"Validační MAE: {val_mae:.2f} g")
        print(f"Čas trénování: {train_time:.2f} s")
        
        results['Random Forest'] = {
            'model': rf_model,
            'val_mae': val_mae,
            'train_time': train_time
        }
        
        # 2. XGBoost s waveletovými příznaky
        print("\n" + "="*60)
        print("XGBOOST S WAVELETOVÝMI PŘÍZNAKY")
        print("="*60)
        
        xgb_model = xgb.XGBRegressor(
            n_estimators=300,
            learning_rate=0.05,
            max_depth=8,
            subsample=0.8,
            colsample_bytree=0.8,
            gamma=0.1,
            reg_alpha=0.1,
            reg_lambda=1.0,
            random_state=42,
            n_jobs=-1
        )
        
        start_time = time.time()
        xgb_model.fit(
            X_train_split, y_train_split,
            eval_set=[(X_val_split, y_val_split)],
            early_stopping_rounds=50,
            verbose=False
        )
        train_time = time.time() - start_time
        
        y_pred_val = xgb_model.predict(X_val_split)
        val_mae = mean_absolute_error(y_val_split, y_pred_val)
        
        print(f"Validační MAE: {val_mae:.2f} g")
        print(f"Čas trénování: {train_time:.2f} s")
        
        results['XGBoost'] = {
            'model': xgb_model,
            'val_mae': val_mae,
            'train_time': train_time
        }
        
        # 3. LightGBM s waveletovými příznaky
        print("\n" + "="*60)
        print("LIGHTGBM S WAVELETOVÝMI PŘÍZNAKY")
        print("="*60)
        
        lgb_model = lgb.LGBMRegressor(
            n_estimators=300,
            learning_rate=0.05,
            max_depth=8,
            num_leaves=50,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.1,
            reg_lambda=1.0,
            random_state=42,
            n_jobs=-1,
            verbose=-1
        )
        
        start_time = time.time()
        lgb_model.fit(
            X_train_split, y_train_split,
            eval_set=[(X_val_split, y_val_split)],
            callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
        )
        train_time = time.time() - start_time
        
        y_pred_val = lgb_model.predict(X_val_split)
        val_mae = mean_absolute_error(y_val_split, y_pred_val)
        
        print(f"Validační MAE: {val_mae:.2f} g")
        print(f"Čas trénování: {train_time:.2f} s")
        
        results['LightGBM'] = {
            'model': lgb_model,
            'val_mae': val_mae,
            'train_time': train_time
        }
        
        # Výběr nejlepšího modelu
        best_model_name = min(results, key=lambda x: results[x]['val_mae'])
        best_model = results[best_model_name]['model']
        
        print("\n" + "="*60)
        print(f"NEJLEPŠÍ MODEL: {best_model_name}")
        print(f"MAE: {results[best_model_name]['val_mae']:.2f} g")
        print("="*60)
        
        # Přetrénování nejlepšího modelu na všech datech
        print("\nPřetrénování nejlepšího modelu na všech datech...")
        if best_model_name == 'XGBoost':
            best_model = xgb.XGBRegressor(**best_model.get_params())
            best_model.fit(X_train_scaled, train_responses)
        elif best_model_name == 'LightGBM':
            best_model = lgb.LGBMRegressor(**best_model.get_params())
            best_model.fit(X_train_scaled, train_responses)
        else:
            best_model.fit(X_train_scaled, train_responses)
        
        # Finální predikce
        print("\nVytváření finálních predikcí...")
        predictions = best_model.predict(X_test_scaled)
        predictions = np.clip(predictions, 0, 19200)
        
        # Uložení
        joblib.dump(best_model, 'best_wavelet_model.pkl')
        joblib.dump(self.scaler, 'wavelet_scaler.pkl')
        joblib.dump(self.feature_extractor, 'wavelet_feature_extractor.pkl')
        np.save('predictions_wavelet.npy', predictions)
        
        print(f"\nPredikce uloženy do 'predictions_wavelet.npy'")
        print(f"Model uložen do 'best_wavelet_model.pkl'")
        
        # Report důležitých příznaků
        if hasattr(best_model, 'feature_importances_'):
            importances = best_model.feature_importances_
            indices = np.argsort(importances)[::-1][:20]
            
            print("\nTOP 20 NEJDŮLEŽITĚJŠÍCH WAVELETOVÝCH PŘÍZNAKŮ:")
            print("-" * 60)
            for i, idx in enumerate(indices):
                print(f"{i+1:2d}. {self.feature_extractor.feature_names[idx]:50s} {importances[idx]:.4f}")
        
        return predictions

def main():
    pipeline = WaveletMLPipeline()
    predictions = pipeline.run_pipeline('../AMSM_LOZISKA_2025/student_data.pkl')
    
    print("\n" + "="*60)
    print("WAVELETOVÝ PIPELINE DOKONČEN!")
    print("="*60)
    print(f"Počet predikcí: {len(predictions)}")
    print(f"Rozsah predikcí: {predictions.min():.2f} - {predictions.max():.2f} g")
    print(f"Průměrná predikce: {predictions.mean():.2f} g")
    print(f"Std. odchylka predikcí: {predictions.std():.2f} g")

if __name__ == "__main__":
    main()