"""
Finální optimalizovaný pipeline kombinuj<PERSON><PERSON><PERSON> nejlep<PERSON> přístupy
pro Ball Bearing Predictive Challenge 2025.
"""

import numpy as np
import pandas as pd
import pickle
from tqdm import tqdm
from scipy.stats import skew, kurtosis
from scipy.signal import welch, find_peaks
from scipy.interpolate import UnivariateSpline

# Import modelů a nástrojů
from sklearn.model_selection import KFold, cross_val_score, train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.pipeline import Pipeline
from sklearn.linear_model import Ridge
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, VotingRegressor
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF, Matern, WhiteKernel
from sklearn.isotonic import IsotonicRegression
from sklearn.metrics import make_scorer, mean_absolute_error
from sklearn.base import BaseEstimator, TransformerMixin

import xgboost as xgb
import lightgbm as lgb
import joblib
import warnings
warnings.filterwarnings('ignore')

print("Všechny knihovny úspěšně naimportovány.")


class SmoteRTransformer(BaseEstimator, TransformerMixin):
    """SMOTE pro regresi - augmentace dat mezi diskrétními úrovněmi."""
    
    def __init__(self, discrete_levels=None, augment_factor=2, noise_std=50):
        self.discrete_levels = discrete_levels or [2500, 7500, 12500, 17500]
        self.augment_factor = augment_factor
        self.noise_std = noise_std
    
    def fit(self, X, y=None):
        return self
    
    def transform(self, X):
        return X
    
    def fit_resample(self, X, y):
        """Vytvoří syntetické vzorky mezi diskrétními úrovněmi."""
        X_aug = [X]
        y_aug = [y]
        
        print("Aplikuji SMOTE-R augmentaci...")
        for i in range(len(self.discrete_levels) - 1):
            level1, level2 = self.discrete_levels[i], self.discrete_levels[i+1]
            
            idx1 = np.where(np.abs(y - level1) < 100)[0]
            idx2 = np.where(np.abs(y - level2) < 100)[0]
            
            if len(idx1) > 0 and len(idx2) > 0:
                n_synthetic = min(len(idx1), len(idx2)) // self.augment_factor
                
                for _ in range(n_synthetic):
                    # Náhodný výběr
                    i1, i2 = np.random.choice(idx1), np.random.choice(idx2)
                    
                    # Interpolace s náhodným faktorem
                    alpha = np.random.uniform(0.2, 0.8)
                    
                    # Syntetický vzorek
                    X_synth = (1 - alpha) * X[i1] + alpha * X[i2]
                    y_synth = (1 - alpha) * level1 + alpha * level2
                    y_synth += np.random.normal(0, self.noise_std)
                    
                    X_aug.append(X_synth.reshape(1, -1))
                    y_aug.append([y_synth])
        
        X_final = np.vstack(X_aug)
        y_final = np.concatenate(y_aug)
        print(f"Augmentace dokončena: {len(X)} -> {len(X_final)} vzorků")
        
        return X_final, y_final


def extract_advanced_features(sample, fs=1.5625e6):
    """
    Pokročilá extrakce příznaků kombinující váš přístup s AE-specifickými příznaky.
    """
    all_features = []
    num_freq_bands = 10
    
    for ch_idx, channel_data in enumerate(sample):
        # === ČASOVÁ DOMÉNA (váš přístup + AE příznaky) ===
        # Základní statistiky
        all_features.extend([
            np.mean(channel_data),
            np.std(channel_data),
            np.var(channel_data),
            skew(channel_data),
            kurtosis(channel_data),
            np.max(channel_data),
            np.min(channel_data),
            np.max(channel_data) - np.min(channel_data),  # Peak-to-peak
        ])
        
        # RMS - kritický pro zatížení ložisek
        rms = np.sqrt(np.mean(channel_data**2))
        all_features.append(rms)
        
        # Crest Factor
        peak = np.max(np.abs(channel_data))
        crest_factor = peak / rms if rms > 0 else 0
        all_features.append(crest_factor)
        
        # AE-specifické příznaky
        # Hit rate - počet AE událostí
        threshold = 3 * np.std(channel_data)
        peaks, _ = find_peaks(np.abs(channel_data), height=threshold)
        hit_rate = len(peaks) / (len(channel_data) / fs)
        all_features.append(hit_rate)
        
        # Energy
        energy = np.sum(channel_data**2)
        all_features.append(np.log(energy + 1e-10))
        
        # === FREKVENČNÍ DOMÉNA ===
        fft_vals = np.abs(np.fft.rfft(channel_data))
        fft_freq = np.fft.rfftfreq(len(channel_data), 1/fs)
        
        all_features.extend([
            np.mean(fft_vals),
            np.std(fft_vals),
            skew(fft_vals),
            kurtosis(fft_vals)
        ])
        
        # Spektrální centroid - důležitý pro AE
        if np.sum(fft_vals) > 0:
            spectral_centroid = np.sum(fft_freq * fft_vals) / np.sum(fft_vals)
        else:
            spectral_centroid = 0
        all_features.append(spectral_centroid)
        
        # Energie ve frekvenčních pásmech (váš přístup)
        band_energies = np.array_split(fft_vals**2, num_freq_bands)
        for band in band_energies:
            all_features.append(np.mean(band))
        
        # Dodatečné AE pásma (0-10kHz, 10-50kHz, 50-100kHz, 100-200kHz, 200-500kHz)
        ae_bands = [(0, 10e3), (10e3, 50e3), (50e3, 100e3), (100e3, 200e3), (200e3, 500e3)]
        for low, high in ae_bands:
            mask = (fft_freq >= low) & (fft_freq < high)
            if np.any(mask):
                band_energy = np.sum(fft_vals[mask]**2)
                all_features.append(band_energy)
            else:
                all_features.append(0)
    
    # === MEZIKANÁLOVÉ PŘÍZNAKY ===
    # Průměrná korelace mezi kanály
    corr_matrix = np.corrcoef(sample)
    upper_triangle = corr_matrix[np.triu_indices(4, k=1)]
    all_features.extend([
        np.mean(upper_triangle),
        np.std(upper_triangle),
        np.max(upper_triangle)
    ])
    
    return np.array(all_features)


class WeightedVotingRegressor(VotingRegressor):
    """Voting regressor s automatickou optimalizací vah."""
    
    def fit(self, X, y):
        # Rozdělení pro validaci vah
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # Trénování base modelů
        super().fit(X_train, y_train)
        
        # Optimalizace vah podle validačního výkonu
        val_scores = []
        for est in self.estimators_:
            pred = est.predict(X_val)
            mae = mean_absolute_error(y_val, pred)
            val_scores.append(1/mae)  # Inverzní MAE jako váha
        
        # Normalizace vah
        total = sum(val_scores)
        self.weights = [s/total for s in val_scores]
        print(f"Optimalizované váhy ensemble: {self.weights}")
        
        # Přetrénování na všech datech
        return super().fit(X, y)


def create_final_pipeline():
    """Vytvoří finální pipeline s nejlepšími komponenty."""
    
    # Kernel pro GPR - Matérn je vhodný pro ne-nekonečně diferencovatelné funkce
    gpr_kernel = Matern(length_scale=1.0, nu=1.5) + WhiteKernel(noise_level=1)
    
    # Definice modelů
    estimators = [
        ('xgb', xgb.XGBRegressor(
            n_estimators=300,
            learning_rate=0.05,
            max_depth=8,
            subsample=0.8,
            colsample_bytree=0.8,
            gamma=0.1,
            reg_alpha=0.1,
            reg_lambda=1.0,
            random_state=42,
            n_jobs=-1
        )),
        ('lgb', lgb.LGBMRegressor(
            n_estimators=300,
            learning_rate=0.05,
            num_leaves=50,
            max_depth=-1,
            min_child_samples=5,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.1,
            reg_lambda=1.0,
            random_state=42,
            n_jobs=-1,
            verbose=-1
        )),
        ('gpr', GaussianProcessRegressor(
            kernel=gpr_kernel,
            alpha=1e-6,
            normalize_y=True,
            n_restarts_optimizer=3,
            random_state=42
        ))
    ]
    
    # Weighted voting ensemble
    ensemble = WeightedVotingRegressor(estimators=estimators, n_jobs=-1)
    
    # Kompletní pipeline
    pipeline = Pipeline([
        ('scaler', RobustScaler()),  # Robustnější vůči outlierům
        ('ensemble', ensemble)
    ])
    
    return pipeline


def main():
    """Hlavní funkce orchestrující celý proces."""
    
    print("="*70)
    print("FINÁLNÍ OPTIMALIZOVANÝ PIPELINE - BALL BEARING CHALLENGE 2025")
    print("="*70)
    
    # Načtení dat
    path_to_pickle = '../AMSM_LOZISKA_2025/student_data.pkl'
    try:
        with open(path_to_pickle, 'rb') as f:
            data = pickle.load(f)
        X_train_raw = data['train_dataset']
        y_train = data['train_response']
        X_test_raw = data['test_dataset']
        print(f"Data úspěšně načtena. Trénovací: {X_train_raw.shape}, Test: {X_test_raw.shape}")
    except FileNotFoundError:
        print(f"Chyba: Soubor '{path_to_pickle}' nebyl nalezen.")
        return
    
    # Extrakce příznaků
    print(f"\nExtrakce pokročilých příznaků z {len(X_train_raw)} trénovacích vzorků...")
    X_train = np.array([extract_advanced_features(sample) for sample in tqdm(X_train_raw)])
    
    print(f"Extrakce příznaků z {len(X_test_raw)} testovacích vzorků...")
    X_test = np.array([extract_advanced_features(sample) for sample in tqdm(X_test_raw)])
    
    print(f"Dimenze příznaků: {X_train.shape[1]} příznaků na vzorek")
    
    # Data augmentation
    smoter = SmoteRTransformer(augment_factor=3, noise_std=50)
    X_train_aug, y_train_aug = smoter.fit_resample(X_train, y_train)
    
    # Cross-validace na augmentovaných datech
    print("\n--- Cross-validace finálního pipeline ---")
    cv_splitter = KFold(n_splits=5, shuffle=True, random_state=42)
    mae_scorer = make_scorer(mean_absolute_error, greater_is_better=False)
    
    pipeline = create_final_pipeline()
    cv_scores = cross_val_score(
        pipeline, X_train_aug, y_train_aug, 
        cv=cv_splitter, scoring=mae_scorer, n_jobs=-1
    )
    
    cv_mae = -np.mean(cv_scores)
    cv_std = np.std(cv_scores)
    print(f"Cross-validace MAE: {cv_mae:.2f} ± {cv_std:.2f} g")
    
    # Trénování finálního modelu
    print("\n--- Trénování finálního modelu na všech datech ---")
    pipeline.fit(X_train_aug, y_train_aug)
    
    # Isotonic regression post-processing
    print("\nKalibrace isotonic regression...")
    train_pred = pipeline.predict(X_train)
    isotonic = IsotonicRegression(out_of_bounds='clip')
    isotonic.fit(train_pred, y_train)
    
    # Finální predikce
    print("\nVytváření finálních predikcí...")
    test_pred_raw = pipeline.predict(X_test)
    test_pred_calibrated = isotonic.predict(test_pred_raw)
    final_predictions = np.clip(test_pred_calibrated, 0, 19200)
    
    # Analýza predikcí
    print("\n--- Analýza finálních predikcí ---")
    print(f"Rozsah: {final_predictions.min():.2f} - {final_predictions.max():.2f} g")
    print(f"Průměr: {final_predictions.mean():.2f} g")
    print(f"Std: {final_predictions.std():.2f} g")
    
    # Distribuce kolem trénovacích úrovní
    train_levels = [2500, 7500, 12500, 17500]
    print("\nDistribuce predikcí kolem trénovacích úrovní:")
    for level in train_levels:
        close = np.sum(np.abs(final_predictions - level) < 500)
        print(f"  ±500g od {level}g: {close} predikcí ({close/len(final_predictions)*100:.1f}%)")
    
    # Uložení
    print("\n--- Ukládání výsledků ---")
    np.save('predictions_final.npy', final_predictions)
    joblib.dump(pipeline, 'final_pipeline.pkl')
    joblib.dump(isotonic, 'isotonic_calibrator.pkl')
    
    # Ukázka s nejistotou (pokud je GPR v ensemble)
    if hasattr(pipeline.named_steps['ensemble'], 'estimators_'):
        for name, est in pipeline.named_steps['ensemble'].estimators_:
            if name == 'gpr':
                print("\nUkázka predikcí s nejistotou (prvních 5 vzorků):")
                X_test_scaled = pipeline.named_steps['scaler'].transform(X_test[:5])
                gpr_pred, gpr_std = est.predict(X_test_scaled, return_std=True)
                for i in range(5):
                    print(f"  Vzorek {i+1}: {gpr_pred[i]:.2f} ± {gpr_std[i]:.2f} g")
                break
    
    print("\n" + "="*70)
    print("PIPELINE ÚSPĚŠNĚ DOKONČEN!")
    print("="*70)
    print("Soubory:")
    print("  - predictions_final.npy : Finální predikce")
    print("  - final_pipeline.pkl : Natrénovaný model")
    print("  - isotonic_calibrator.pkl : Kalibrátor pro post-processing")


if __name__ == "__main__":
    main()