# Import oprava pro feature_engineering

import numpy as np
import pandas as pd
import pickle
import joblib
from sklearn.model_selection import train_test_split, KFold, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb
import lightgbm as lgb
from feature_engineering import FeatureExtractor
import matplotlib.pyplot as plt
import seaborn as sns
import time
from typing import Dict, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

class BearingLoadPredictor:
    """Pipeline pro predikci zatížení ložisek."""
    
    def __init__(self):
        self.feature_extractor = FeatureExtractor()
        self.scaler = None
        self.best_model = None
        self.results = {}
        
    def load_data(self, file_path: str) -> <PERSON><PERSON>[np.ndarray, np.ndarray, np.ndarray]:
        """Načte data ze souboru."""
        print("Načítání dat...")
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
        
        train_data = data['train_dataset']
        train_responses = data['train_response']
        eval_data = data['test_dataset']
        
        print(f"Trénovací data: {train_data.shape}")
        print(f"Testovací data: {eval_data.shape}")
        
        return train_data, train_responses, eval_data
    
    def extract_features(self, train_data: np.ndarray, eval_data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Extrahuje příznaky z dat."""
        print("\nExtrakce příznaků z trénovacích dat...")
        X_train = self.feature_extractor.transform(train_data)
        
        print("\nExtrakce příznaků z testovacích dat...")
        X_eval = self.feature_extractor.transform(eval_data)
        
        # Uloží názvy příznaků
        self.feature_names = self.feature_extractor.get_feature_names()
        
        print(f"\nPočet příznaků: {X_train.shape[1]}")
        
        return X_train, X_eval
    
    def preprocess_features(self, X_train: np.ndarray, X_eval: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Normalizuje příznaky."""
        print("\nNormalizace příznaků...")
        
        # Použij RobustScaler pro robustnost vůči outlierům
        self.scaler = RobustScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_eval_scaled = self.scaler.transform(X_eval)
        
        # Nahraď NaN a inf hodnoty
        X_train_scaled = np.nan_to_num(X_train_scaled, nan=0.0, posinf=0.0, neginf=0.0)
        X_eval_scaled = np.nan_to_num(X_eval_scaled, nan=0.0, posinf=0.0, neginf=0.0)
        
        return X_train_scaled, X_eval_scaled
    
    def evaluate_model(self, model, X: np.ndarray, y: np.ndarray, cv_folds: int = 5) -> Dict[str, float]:
        """Vyhodnotí model pomocí křížové validace."""
        kfold = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
        
        # MAE scores
        mae_scores = -cross_val_score(model, X, y, cv=kfold, scoring='neg_mean_absolute_error', n_jobs=-1)
        
        # MSE scores
        mse_scores = -cross_val_score(model, X, y, cv=kfold, scoring='neg_mean_squared_error', n_jobs=-1)
        
        # R2 scores
        r2_scores = cross_val_score(model, X, y, cv=kfold, scoring='r2', n_jobs=-1)
        
        results = {
            'mae_mean': np.mean(mae_scores),
            'mae_std': np.std(mae_scores),
            'mse_mean': np.mean(mse_scores),
            'mse_std': np.std(mse_scores),
            'rmse_mean': np.sqrt(np.mean(mse_scores)),
            'r2_mean': np.mean(r2_scores),
            'r2_std': np.std(r2_scores)
        }
        
        return results
    
    def train_baseline_models(self, X_train: np.ndarray, y_train: np.ndarray) -> None:
        """Trénuje baseline modely."""
        print("\n" + "="*60)
        print("TRÉNOVÁNÍ BASELINE MODELŮ")
        print("="*60)
        
        baseline_models = {
            'Linear Regression': LinearRegression(),
            'Ridge Regression': Ridge(alpha=1.0),
            'Lasso Regression': Lasso(alpha=1.0, max_iter=2000),
            'ElasticNet': ElasticNet(alpha=1.0, l1_ratio=0.5, max_iter=2000)
        }
        
        for name, model in baseline_models.items():
            print(f"\n{name}:")
            start_time = time.time()
            
            # Vyhodnoť model
            results = self.evaluate_model(model, X_train, y_train)
            
            # Natrénuj na celých datech pro pozdější použití
            model.fit(X_train, y_train)
            
            elapsed_time = time.time() - start_time
            
            print(f"  MAE: {results['mae_mean']:.2f} ± {results['mae_std']:.2f}")
            print(f"  RMSE: {results['rmse_mean']:.2f}")
            print(f"  R²: {results['r2_mean']:.4f} ± {results['r2_std']:.4f}")
            print(f"  Čas: {elapsed_time:.2f}s")
            
            self.results[name] = {
                'model': model,
                'results': results,
                'time': elapsed_time
            }
    
    def train_tree_models(self, X_train: np.ndarray, y_train: np.ndarray) -> None:
        """Trénuje stromové modely."""
        print("\n" + "="*60)
        print("TRÉNOVÁNÍ STROMOVÝCH MODELŮ")
        print("="*60)
        
        # Random Forest
        print("\nRandom Forest:")
        start_time = time.time()
        
        rf_model = RandomForestRegressor(
            n_estimators=100,
            max_depth=20,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )
        
        results = self.evaluate_model(rf_model, X_train, y_train)
        rf_model.fit(X_train, y_train)
        
        elapsed_time = time.time() - start_time
        
        print(f"  MAE: {results['mae_mean']:.2f} ± {results['mae_std']:.2f}")
        print(f"  RMSE: {results['rmse_mean']:.2f}")
        print(f"  R²: {results['r2_mean']:.4f} ± {results['r2_std']:.4f}")
        print(f"  Čas: {elapsed_time:.2f}s")
        
        self.results['Random Forest'] = {
            'model': rf_model,
            'results': results,
            'time': elapsed_time
        }
        
        # Gradient Boosting
        print("\nGradient Boosting:")
        start_time = time.time()
        
        gb_model = GradientBoostingRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=5,
            random_state=42
        )
        
        results = self.evaluate_model(gb_model, X_train, y_train)
        gb_model.fit(X_train, y_train)
        
        elapsed_time = time.time() - start_time
        
        print(f"  MAE: {results['mae_mean']:.2f} ± {results['mae_std']:.2f}")
        print(f"  RMSE: {results['rmse_mean']:.2f}")
        print(f"  R²: {results['r2_mean']:.4f} ± {results['r2_std']:.4f}")
        print(f"  Čas: {elapsed_time:.2f}s")
        
        self.results['Gradient Boosting'] = {
            'model': gb_model,
            'results': results,
            'time': elapsed_time
        }
        
        # XGBoost
        print("\nXGBoost:")
        start_time = time.time()
        
        xgb_model = xgb.XGBRegressor(
            n_estimators=200,
            learning_rate=0.1,
            max_depth=6,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            n_jobs=-1
        )
        
        results = self.evaluate_model(xgb_model, X_train, y_train)
        xgb_model.fit(X_train, y_train)
        
        elapsed_time = time.time() - start_time
        
        print(f"  MAE: {results['mae_mean']:.2f} ± {results['mae_std']:.2f}")
        print(f"  RMSE: {results['rmse_mean']:.2f}")
        print(f"  R²: {results['r2_mean']:.4f} ± {results['r2_std']:.4f}")
        print(f"  Čas: {elapsed_time:.2f}s")
        
        self.results['XGBoost'] = {
            'model': xgb_model,
            'results': results,
            'time': elapsed_time
        }
        
        # LightGBM
        print("\nLightGBM:")
        start_time = time.time()
        
        lgb_model = lgb.LGBMRegressor(
            n_estimators=200,
            learning_rate=0.1,
            max_depth=6,
            num_leaves=31,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            n_jobs=-1,
            verbose=-1
        )
        
        results = self.evaluate_model(lgb_model, X_train, y_train)
        lgb_model.fit(X_train, y_train)
        
        elapsed_time = time.time() - start_time
        
        print(f"  MAE: {results['mae_mean']:.2f} ± {results['mae_std']:.2f}")
        print(f"  RMSE: {results['rmse_mean']:.2f}")
        print(f"  R²: {results['r2_mean']:.4f} ± {results['r2_std']:.4f}")
        print(f"  Čas: {elapsed_time:.2f}s")
        
        self.results['LightGBM'] = {
            'model': lgb_model,
            'results': results,
            'time': elapsed_time
        }
    
    def hyperparameter_tuning(self, X_train: np.ndarray, y_train: np.ndarray, model_name: str = 'XGBoost') -> None:
        """Ladění hyperparametrů pro nejlepší model."""
        print("\n" + "="*60)
        print(f"LADĚNÍ HYPERPARAMETRŮ PRO {model_name}")
        print("="*60)
        
        # Rozdělení na trénovací a validační set pro rychlejší ladění
        X_tune, X_val, y_tune, y_val = train_test_split(X_train, y_train, test_size=0.2, random_state=42)
        
        if model_name == 'XGBoost':
            param_grid = {
                'n_estimators': [100, 200, 300],
                'learning_rate': [0.05, 0.1, 0.15],
                'max_depth': [4, 6, 8],
                'subsample': [0.7, 0.8, 0.9],
                'colsample_bytree': [0.7, 0.8, 0.9]
            }
            
            model = xgb.XGBRegressor(random_state=42, n_jobs=-1)
            
        elif model_name == 'LightGBM':
            param_grid = {
                'n_estimators': [100, 200, 300],
                'learning_rate': [0.05, 0.1, 0.15],
                'max_depth': [4, 6, 8],
                'num_leaves': [31, 50, 70],
                'subsample': [0.7, 0.8, 0.9]
            }
            
            model = lgb.LGBMRegressor(random_state=42, n_jobs=-1, verbose=-1)
        
        else:
            print(f"Model {model_name} není podporován pro ladění hyperparametrů.")
            return
        
        # Grid search s křížovou validací
        grid_search = GridSearchCV(
            model,
            param_grid,
            cv=3,
            scoring='neg_mean_absolute_error',
            n_jobs=-1,
            verbose=1
        )
        
        print("Hledání nejlepších hyperparametrů...")
        grid_search.fit(X_tune, y_tune)
        
        print(f"\nNejlepší parametry: {grid_search.best_params_}")
        print(f"Nejlepší MAE (CV): {-grid_search.best_score_:.2f}")
        
        # Validace na validačním setu
        best_model = grid_search.best_estimator_
        y_pred_val = best_model.predict(X_val)
        val_mae = mean_absolute_error(y_val, y_pred_val)
        print(f"MAE na validačním setu: {val_mae:.2f}")
        
        # Přetrénování na celých datech
        print("\nPřetrénování modelu s nejlepšími parametry na celých datech...")
        best_model.fit(X_train, y_train)
        
        self.results[f'{model_name}_tuned'] = {
            'model': best_model,
            'best_params': grid_search.best_params_,
            'cv_mae': -grid_search.best_score_,
            'val_mae': val_mae
        }
        
        return best_model
    
    def plot_results_comparison(self) -> None:
        """Vykreslí porovnání výsledků modelů."""
        # Připrav data pro vizualizaci
        model_names = []
        mae_means = []
        mae_stds = []
        r2_means = []
        
        for name, data in self.results.items():
            if 'results' in data:
                model_names.append(name)
                mae_means.append(data['results']['mae_mean'])
                mae_stds.append(data['results']['mae_std'])
                r2_means.append(data['results']['r2_mean'])
        
        # Vytvoř graf
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # MAE porovnání
        x = np.arange(len(model_names))
        ax1.bar(x, mae_means, yerr=mae_stds, capsize=5, alpha=0.7)
        ax1.set_xlabel('Model')
        ax1.set_ylabel('MAE [g]')
        ax1.set_title('Průměrná absolutní chyba (MAE) - nižší je lepší')
        ax1.set_xticks(x)
        ax1.set_xticklabels(model_names, rotation=45, ha='right')
        ax1.grid(True, alpha=0.3)
        
        # R² porovnání
        ax2.bar(x, r2_means, alpha=0.7, color='green')
        ax2.set_xlabel('Model')
        ax2.set_ylabel('R²')
        ax2.set_title('Koeficient determinace (R²) - vyšší je lepší')
        ax2.set_xticks(x)
        ax2.set_xticklabels(model_names, rotation=45, ha='right')
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 1)
        
        plt.tight_layout()
        plt.savefig('model_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_best_model_report(self) -> None:
        """Uloží report s výsledky."""
        report = []
        report.append("="*80)
        report.append("VÝSLEDKY ML PIPELINE PRO PREDIKCI ZATÍŽENÍ LOŽISEK")
        report.append("="*80)
        report.append("")
        
        # Seřaď modely podle MAE
        sorted_models = sorted(
            [(name, data) for name, data in self.results.items() if 'results' in data],
            key=lambda x: x[1]['results']['mae_mean']
        )
        
        report.append("POROVNÁNÍ MODELŮ (seřazeno podle MAE):")
        report.append("-"*60)
        
        for i, (name, data) in enumerate(sorted_models):
            results = data['results']
            report.append(f"\n{i+1}. {name}")
            report.append(f"   MAE: {results['mae_mean']:.2f} ± {results['mae_std']:.2f} g")
            report.append(f"   RMSE: {results['rmse_mean']:.2f} g")
            report.append(f"   R²: {results['r2_mean']:.4f} ± {results['r2_std']:.4f}")
            if 'time' in data:
                report.append(f"   Čas trénování: {data['time']:.2f} s")
        
        # Nejlepší model
        best_name, best_data = sorted_models[0]
        report.append("\n" + "="*60)
        report.append(f"NEJLEPŠÍ MODEL: {best_name}")
        report.append("="*60)
        
        # Důležité příznaky (pokud model podporuje)
        if hasattr(best_data['model'], 'feature_importances_'):
            importances = best_data['model'].feature_importances_
            indices = np.argsort(importances)[::-1][:20]
            
            report.append("\nTOP 20 NEJDŮLEŽITĚJŠÍCH PŘÍZNAKŮ:")
            report.append("-"*40)
            
            for i, idx in enumerate(indices):
                report.append(f"{i+1:2d}. {self.feature_names[idx]:40s} {importances[idx]:.4f}")
        
        # Uložení reportu
        with open('ml_results_report.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print("\nReport uložen do 'ml_results_report.txt'")
        
        return best_name, best_data['model']
    
    def make_predictions(self, model: Any, X_eval: np.ndarray) -> np.ndarray:
        """Vytvoří predikce pro testovací data."""
        print("\nVytváření predikcí...")
        predictions = model.predict(X_eval)
        
        # Zajisti, že predikce jsou v rozumném rozsahu
        predictions = np.clip(predictions, 0, 19200)
        
        return predictions
    
    def run_full_pipeline(self, data_path: str) -> np.ndarray:
        """Spustí celý pipeline."""
        # Načti data
        train_data, train_responses, eval_data = self.load_data(data_path)
        
        # Extrakce příznaků
        X_train, X_eval = self.extract_features(train_data, eval_data)
        
        # Ulož extrahované příznaky pro případné další použití
        np.save('X_train_features.npy', X_train)
        np.save('X_eval_features.npy', X_eval)
        np.save('y_train.npy', train_responses)
        
        # Normalizace
        X_train_scaled, X_eval_scaled = self.preprocess_features(X_train, X_eval)
        
        # Trénování modelů
        self.train_baseline_models(X_train_scaled, train_responses)
        self.train_tree_models(X_train_scaled, train_responses)
        
        # Porovnání výsledků
        self.plot_results_comparison()
        
        # Ladění hyperparametrů pro nejlepší model
        best_model = self.hyperparameter_tuning(X_train_scaled, train_responses, 'XGBoost')
        
        # Uložení reportu a výběr nejlepšího modelu
        best_name, final_model = self.save_best_model_report()
        
        # Finální predikce
        if 'tuned' in self.results and self.results['XGBoost_tuned']['val_mae'] < self.results[best_name]['results']['mae_mean']:
            print("\nPoužívám vyladěný XGBoost model pro finální predikce.")
            final_model = best_model
        
        predictions = self.make_predictions(final_model, X_eval_scaled)
        
        # Uložení modelu a predikcí
        joblib.dump(final_model, 'best_ml_model.pkl')
        joblib.dump(self.scaler, 'feature_scaler.pkl')
        np.save('predictions_ml_baseline.npy', predictions)
        
        print(f"\nPredikce uloženy do 'predictions_ml_baseline.npy'")
        print(f"Model uložen do 'best_ml_model.pkl'")
        
        return predictions

def main():
    pipeline = BearingLoadPredictor()
    predictions = pipeline.run_full_pipeline('../AMSM_LOZISKA_2025/student_data.pkl')
    
    print("\n" + "="*60)
    print("PIPELINE DOKONČEN!")
    print("="*60)
    print(f"Počet predikcí: {len(predictions)}")
    print(f"Rozsah predikcí: {predictions.min():.2f} - {predictions.max():.2f} g")
    print(f"Průměrná predikce: {predictions.mean():.2f} g")
    print(f"Std. odchylka predikcí: {predictions.std():.2f} g")

if __name__ == "__main__":
    main()