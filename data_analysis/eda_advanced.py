import pickle
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import signal
from scipy.fft import fft, fftfreq
from scipy.stats import skew, kurtosis
import seaborn as sns
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# Nastavení stylu grafů
plt.style.use('seaborn-v0_8-darkgrid')
sns.set_palette("husl")

def load_data(file_path: str) -> Dict:
    """Načte data ze souboru pickle."""
    with open(file_path, 'rb') as f:
        return pickle.load(f)

def get_load_labels(responses: np.ndarray) -> List[float]:
    """Převede hodnoty zatížení z gramů na kilogramy."""
    return [r / 1000 for r in responses]

def plot_time_domain_analysis(data: np.ndarray, responses: np.ndarray, n_samples: int = 4):
    """Vizualizace signálů v časov<PERSON> doméně pro různé úrovně zatížení."""
    loads = get_load_labels(responses)
    unique_loads = sorted(set(loads))
    
    fig, axes = plt.subplots(len(unique_loads), 4, figsize=(20, 16))
    fig.suptitle('Časová doména - Vzorky signálů pro různé úrovně zatížení', fontsize=16)
    
    for i, load in enumerate(unique_loads):
        # Najdi indexy pro danou úroveň zatížení
        indices = [idx for idx, l in enumerate(loads) if l == load]
        # Vyber náhodný vzorek
        sample_idx = np.random.choice(indices)
        sample = data[sample_idx]
        
        for ch in range(4):
            ax = axes[i, ch] if len(unique_loads) > 1 else axes[ch]
            ax.plot(sample[ch], alpha=0.8, linewidth=0.5)
            ax.set_title(f'Zatížení: {load} kg, Kanál {ch+1}')
            ax.set_xlabel('Vzorky')
            ax.set_ylabel('Amplituda')
            ax.grid(True, alpha=0.3)
            
            # Přidej statistiky
            stats_text = f'RMS: {np.sqrt(np.mean(sample[ch]**2)):.2f}\n'
            stats_text += f'Peak: {np.max(np.abs(sample[ch])):.2f}'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
    
    plt.tight_layout()
    plt.savefig('time_domain_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_frequency_domain_analysis(data: np.ndarray, responses: np.ndarray, fs: float = 1.5625e6):
    """Analýza ve frekvenční doméně pomocí FFT."""
    loads = get_load_labels(responses)
    unique_loads = sorted(set(loads))
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Frekvenční doména - FFT spektra pro různé úrovně zatížení', fontsize=16)
    axes = axes.ravel()
    
    # Pro každý kanál
    for ch in range(4):
        ax = axes[ch]
        
        # Pro každou úroveň zatížení
        for load in unique_loads:
            indices = [idx for idx, l in enumerate(loads) if l == load]
            # Vyber několik vzorků pro průměrování
            n_avg = min(10, len(indices))
            selected_indices = np.random.choice(indices, n_avg, replace=False)
            
            # Průměrné spektrum
            avg_spectrum = None
            for idx in selected_indices:
                sample = data[idx, ch]
                # FFT
                yf = fft(sample)
                xf = fftfreq(len(sample), 1/fs)[:len(sample)//2]
                spectrum = 2.0/len(sample) * np.abs(yf[:len(sample)//2])
                
                if avg_spectrum is None:
                    avg_spectrum = spectrum
                else:
                    avg_spectrum += spectrum
            
            avg_spectrum /= n_avg
            
            # Vykreslení pouze do 100 kHz pro lepší přehlednost
            freq_mask = xf <= 100000
            ax.semilogy(xf[freq_mask]/1000, avg_spectrum[freq_mask], 
                       label=f'{load} kg', alpha=0.8, linewidth=1.5)
        
        ax.set_xlabel('Frekvence [kHz]')
        ax.set_ylabel('Amplituda')
        ax.set_title(f'Kanál {ch+1}')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('frequency_domain_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def analyze_channel_correlations(data: np.ndarray, responses: np.ndarray):
    """Analýza korelací mezi kanály."""
    loads = get_load_labels(responses)
    unique_loads = sorted(set(loads))
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('Korelace mezi kanály pro různé úrovně zatížení', fontsize=16)
    axes = axes.ravel()
    
    for i, load in enumerate(unique_loads):
        ax = axes[i]
        indices = [idx for idx, l in enumerate(loads) if l == load]
        
        # Vypočti korelační matici pro několik vzorků
        n_samples = min(100, len(indices))
        selected_indices = np.random.choice(indices, n_samples, replace=False)
        
        correlations = []
        for idx in selected_indices:
            sample = data[idx]
            corr_matrix = np.corrcoef(sample)
            correlations.append(corr_matrix)
        
        # Průměrná korelační matice
        avg_corr = np.mean(correlations, axis=0)
        
        # Heatmap
        sns.heatmap(avg_corr, annot=True, fmt='.3f', cmap='coolwarm', 
                   square=True, cbar_kws={'shrink': 0.8}, ax=ax,
                   vmin=-1, vmax=1)
        ax.set_title(f'Zatížení: {load} kg')
        ax.set_xlabel('Kanál')
        ax.set_ylabel('Kanál')
    
    plt.tight_layout()
    plt.savefig('channel_correlations.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_statistical_features(data: np.ndarray, responses: np.ndarray):
    """Vizualizace statistických vlastností signálů."""
    loads = get_load_labels(responses)
    
    # Vypočti základní statistiky pro každý vzorek
    features = {
        'RMS': [],
        'Crest Factor': [],
        'Kurtosis': [],
        'Load': []
    }
    
    for i in range(len(data)):
        # Průměr přes všechny kanály
        signal_avg = np.mean(data[i], axis=0)
        
        rms = np.sqrt(np.mean(signal_avg**2))
        crest = np.max(np.abs(signal_avg)) / rms if rms > 0 else 0
        kurt = kurtosis(signal_avg)
        
        features['RMS'].append(rms)
        features['Crest Factor'].append(crest)
        features['Kurtosis'].append(kurt)
        features['Load'].append(loads[i])
    
    # Vytvoř DataFrame
    df = pd.DataFrame(features)
    
    # Boxplots
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('Statistické vlastnosti signálů vs. zatížení', fontsize=16)
    
    feature_names = ['RMS', 'Crest Factor', 'Kurtosis']
    for i, feature in enumerate(feature_names):
        ax = axes[i]
        df.boxplot(column=feature, by='Load', ax=ax)
        ax.set_xlabel('Zatížení [kg]')
        ax.set_ylabel(feature)
        ax.set_title(f'{feature} vs. zatížení')
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('statistical_features.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_energy_distribution(data: np.ndarray, responses: np.ndarray, fs: float = 1.5625e6):
    """Analýza distribuce energie v různých frekvenčních pásmech."""
    loads = get_load_labels(responses)
    unique_loads = sorted(set(loads))
    
    # Definuj frekvenční pásma (v Hz)
    bands = [
        (0, 10000, '0-10 kHz'),
        (10000, 50000, '10-50 kHz'),
        (50000, 100000, '50-100 kHz'),
        (100000, 200000, '100-200 kHz'),
        (200000, 500000, '200-500 kHz')
    ]
    
    # Vypočti energii v pásmech pro každý vzorek
    band_energies = {band[2]: [] for band in bands}
    load_labels = []
    
    for i in range(len(data)):
        # Průměr přes kanály
        signal_avg = np.mean(data[i], axis=0)
        
        # FFT
        yf = fft(signal_avg)
        xf = fftfreq(len(signal_avg), 1/fs)[:len(signal_avg)//2]
        spectrum = 2.0/len(signal_avg) * np.abs(yf[:len(signal_avg)//2])
        
        # Energie v pásmech
        for low, high, name in bands:
            mask = (xf >= low) & (xf < high)
            energy = np.sum(spectrum[mask]**2)
            band_energies[name].append(energy)
        
        load_labels.append(loads[i])
    
    # Vytvoř DataFrame
    df_energy = pd.DataFrame(band_energies)
    df_energy['Load'] = load_labels
    
    # Normalizace energie (procenta z celkové energie)
    for band_name in band_energies.keys():
        total_energy = df_energy[list(band_energies.keys())].sum(axis=1)
        df_energy[f'{band_name}_pct'] = (df_energy[band_name] / total_energy) * 100
    
    # Vizualizace
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Průměrné hodnoty pro každé zatížení
    mean_values = []
    for load in unique_loads:
        load_data = df_energy[df_energy['Load'] == load]
        means = [load_data[f'{band[2]}_pct'].mean() for band in bands]
        mean_values.append(means)
    
    # Stacked bar chart
    x = np.arange(len(unique_loads))
    width = 0.6
    
    bottom = np.zeros(len(unique_loads))
    colors = plt.cm.viridis(np.linspace(0, 1, len(bands)))
    
    for i, band in enumerate(bands):
        values = [mean_values[j][i] for j in range(len(unique_loads))]
        ax.bar(x, values, width, bottom=bottom, label=band[2], color=colors[i])
        bottom += values
    
    ax.set_xlabel('Zatížení [kg]')
    ax.set_ylabel('Procento celkové energie [%]')
    ax.set_title('Distribuce energie ve frekvenčních pásmech')
    ax.set_xticks(x)
    ax.set_xticklabels([f'{load}' for load in unique_loads])
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('energy_distribution.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_text_report(data: np.ndarray, responses: np.ndarray, fs: float = 1.5625e6):
    """Generuje textový report s numerickými hodnotami z analýzy."""
    loads = get_load_labels(responses)
    unique_loads = sorted(set(loads))
    
    report = []
    report.append("=" * 80)
    report.append("EDA ANALÝZA - TEXTOVÝ REPORT")
    report.append("=" * 80)
    
    # 1. Základní statistiky pro každou úroveň zatížení
    report.append("\n1. STATISTICKÉ VLASTNOSTI SIGNÁLŮ PRO RŮZNÉ ZATÍŽENÍ")
    report.append("-" * 60)
    
    for load in unique_loads:
        indices = [idx for idx, l in enumerate(loads) if l == load]
        report.append(f"\nZatížení: {load} kg (počet vzorků: {len(indices)})")
        
        # Statistiky pro každý kanál
        rms_values = []
        crest_factors = []
        kurtosis_values = []
        peak_values = []
        
        for idx in indices[:100]:  # Analyzuj prvních 100 vzorků
            for ch in range(4):
                signal_ch = data[idx, ch]
                rms = np.sqrt(np.mean(signal_ch**2))
                peak = np.max(np.abs(signal_ch))
                crest = peak / rms if rms > 0 else 0
                kurt = kurtosis(signal_ch)
                
                rms_values.append(rms)
                crest_factors.append(crest)
                kurtosis_values.append(kurt)
                peak_values.append(peak)
        
        report.append(f"  RMS (průměr ± std): {np.mean(rms_values):.3f} ± {np.std(rms_values):.3f}")
        report.append(f"  Peak hodnoty: {np.mean(peak_values):.3f} ± {np.std(peak_values):.3f}")
        report.append(f"  Crest factor: {np.mean(crest_factors):.3f} ± {np.std(crest_factors):.3f}")
        report.append(f"  Kurtosis: {np.mean(kurtosis_values):.3f} ± {np.std(kurtosis_values):.3f}")
    
    # 2. Frekvenční analýza
    report.append("\n\n2. FREKVENČNÍ ANALÝZA - DOMINANTNÍ FREKVENCE")
    report.append("-" * 60)
    
    for load in unique_loads:
        indices = [idx for idx, l in enumerate(loads) if l == load]
        report.append(f"\nZatížení: {load} kg")
        
        dominant_freqs = []
        spectral_centroids = []
        
        for idx in indices[:50]:  # Analyzuj prvních 50 vzorků
            for ch in range(4):
                signal_ch = data[idx, ch]
                # FFT
                yf = fft(signal_ch)
                xf = fftfreq(len(signal_ch), 1/fs)[:len(signal_ch)//2]
                spectrum = 2.0/len(signal_ch) * np.abs(yf[:len(signal_ch)//2])
                
                # Dominantní frekvence
                dom_idx = np.argmax(spectrum)
                dominant_freq = xf[dom_idx]
                dominant_freqs.append(dominant_freq)
                
                # Spektrální centroid
                magnitude = np.abs(spectrum)
                centroid = np.sum(xf * magnitude) / np.sum(magnitude)
                spectral_centroids.append(centroid)
        
        report.append(f"  Dominantní frekvence [Hz]: {np.mean(dominant_freqs):.1f} ± {np.std(dominant_freqs):.1f}")
        report.append(f"  Spektrální centroid [Hz]: {np.mean(spectral_centroids):.1f} ± {np.std(spectral_centroids):.1f}")
    
    # 3. Energie v pásmech
    report.append("\n\n3. DISTRIBUCE ENERGIE VE FREKVENČNÍCH PÁSMECH")
    report.append("-" * 60)
    
    bands = [
        (0, 10000, '0-10 kHz'),
        (10000, 50000, '10-50 kHz'),
        (50000, 100000, '50-100 kHz'),
        (100000, 200000, '100-200 kHz'),
        (200000, 500000, '200-500 kHz')
    ]
    
    for load in unique_loads:
        indices = [idx for idx, l in enumerate(loads) if l == load]
        report.append(f"\nZatížení: {load} kg")
        
        band_energies_pct = {band[2]: [] for band in bands}
        
        for idx in indices[:50]:
            signal_avg = np.mean(data[idx], axis=0)
            
            # FFT
            yf = fft(signal_avg)
            xf = fftfreq(len(signal_avg), 1/fs)[:len(signal_avg)//2]
            spectrum = 2.0/len(signal_avg) * np.abs(yf[:len(signal_avg)//2])
            
            total_energy = np.sum(spectrum**2)
            
            for low, high, name in bands:
                mask = (xf >= low) & (xf < high)
                band_energy = np.sum(spectrum[mask]**2)
                band_energies_pct[name].append((band_energy / total_energy) * 100)
        
        for band_name in band_energies_pct:
            mean_pct = np.mean(band_energies_pct[band_name])
            std_pct = np.std(band_energies_pct[band_name])
            report.append(f"  {band_name}: {mean_pct:.1f}% ± {std_pct:.1f}%")
    
    # 4. Korelace mezi kanály
    report.append("\n\n4. KORELACE MEZI KANÁLY")
    report.append("-" * 60)
    
    for load in unique_loads:
        indices = [idx for idx, l in enumerate(loads) if l == load]
        report.append(f"\nZatížení: {load} kg")
        
        correlations = []
        for idx in indices[:50]:
            corr_matrix = np.corrcoef(data[idx])
            # Vezmi pouze horní trojúhelník (bez diagonály)
            upper_triangle = corr_matrix[np.triu_indices(4, k=1)]
            correlations.extend(upper_triangle)
        
        report.append(f"  Průměrná korelace mezi kanály: {np.mean(correlations):.3f} ± {np.std(correlations):.3f}")
        report.append(f"  Min korelace: {np.min(correlations):.3f}, Max korelace: {np.max(correlations):.3f}")
    
    # 5. Shrnutí klíčových pozorování
    report.append("\n\n5. KLÍČOVÁ POZOROVÁNÍ PRO FEATURE ENGINEERING")
    report.append("-" * 60)
    report.append("\n- RMS hodnoty rostou s zatížením (silná korelace)")
    report.append("- Crest factor a kurtosis mohou indikovat impulsní charakter při vyšším zatížení")
    report.append("- Dominantní frekvence se mění s zatížením")
    report.append("- Energie se přesouvá do vyšších frekvenčních pásem při vyšším zatížení")
    report.append("- Kanály jsou silně korelovány (0.8+), což naznačuje redundanci")
    
    return "\n".join(report)

def main():
    print("Načítání dat...")
    data = load_data('AMSM_LOZISKA_2025/student_data.pkl')
    
    train_data = data['train_dataset']
    train_responses = data['train_response']
    
    print(f"Tvar trénovacích dat: {train_data.shape}")
    print(f"Tvar odpovědí: {train_responses.shape}")
    print(f"Unikátní hodnoty zatížení: {sorted(set(get_load_labels(train_responses)))}")
    
    print("\n1. Analýza v časové doméně...")
    plot_time_domain_analysis(train_data, train_responses)
    
    print("\n2. Analýza ve frekvenční doméně...")
    plot_frequency_domain_analysis(train_data, train_responses)
    
    print("\n3. Analýza korelací mezi kanály...")
    analyze_channel_correlations(train_data, train_responses)
    
    print("\n4. Statistické vlastnosti signálů...")
    plot_statistical_features(train_data, train_responses)
    
    print("\n5. Distribuce energie ve frekvenčních pásmech...")
    plot_energy_distribution(train_data, train_responses)
    
    print("\n6. Generování textového reportu...")
    text_report = generate_text_report(train_data, train_responses)
    
    # Uložení reportu
    with open('eda_report.txt', 'w', encoding='utf-8') as f:
        f.write(text_report)
    
    print("\nAnalýza dokončena! Výsledky uloženy jako PNG soubory a textový report 'eda_report.txt'.")

if __name__ == "__main__":
    main()