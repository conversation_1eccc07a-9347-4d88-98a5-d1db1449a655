import pickle
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal

# --- Constants ---
DATA_PATH = 'AMSM_LOZISKA_2025/student_data.pkl'
FS = 1.5625e6  # Sampling frequency in Hz
LOAD_LEVELS_KG = [2.5, 7.5, 12.5, 17.5]
LOAD_LEVELS_G = [int(l * 1000) for l in LOAD_LEVELS_KG] # Loads in grams

def load_data(path):
    """Loads the bearing data from a pickle file."""
    try:
        with open(path, 'rb') as pickle_file:
            data = pickle.load(pickle_file)
        train_dataset = data.get('train_dataset')
        train_response = data.get('train_response')
        eval_dataset = data.get('test_dataset') # Keep variable name consistent with notebook
        print(f"Data loaded successfully from {path}")
        print(f'train_dataset shape: {train_dataset.shape}')
        print(f'train_response shape: {train_response.shape}')
        print(f'eval_dataset shape: {eval_dataset.shape}')
        return train_dataset, train_response, eval_dataset
    except FileNotFoundError:
        print(f"Error: Data file not found at {path}")
        return None, None, None
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None

def plot_spectrogram(time_signal, fs, load_g, channel_idx, sample_idx):
    """Calculates and plots the spectrogram for a given signal.
    Also calculates and prints a simple stationarity metric.
    """
    nperseg = 256  # Segment length for STFT
    noverlap = nperseg // 2 # Overlap between segments

    f, t, Sxx = signal.spectrogram(time_signal, fs, nperseg=nperseg, noverlap=noverlap)

    # --- Calculate Stationarity Metric ---
    # Calculate std deviation across time for each frequency bin
    std_dev_across_time = np.std(Sxx, axis=1)
    # Calculate the mean of these standard deviations
    mean_std_dev = np.mean(std_dev_across_time)
    print(f"    Stationarity Metric (Mean Std Dev across Time): {mean_std_dev:.4e}")
    # -------------------------------------

    plt.figure(figsize=(12, 8))

    # Plot time signal
    plt.subplot(2, 1, 1)
    time_axis = np.arange(len(time_signal)) / fs
    plt.plot(time_axis, time_signal)
    plt.title(f'Sample {sample_idx} - Load: {load_g}g - Channel {channel_idx+1} - Time Signal')
    plt.xlabel('Time [s]')
    plt.ylabel('Amplitude')
    plt.grid(True)

    # Plot spectrogram
    plt.subplot(2, 1, 2)
    # Use pcolormesh with logarithmic scale for power
    plt.pcolormesh(t, f, 10 * np.log10(Sxx), shading='gouraud')
    plt.ylabel('Frequency [Hz]')
    plt.xlabel('Time [sec]')
    plt.title('Spectrogram (dB)')
    plt.colorbar(label='Intensity [dB]')
    # Limit frequency axis for better visibility if needed (e.g., up to fs/2 or lower)
    # plt.ylim(0, fs / 2)
    plt.ylim(0, 200000) # Limit to 200 kHz for visualization

    plt.tight_layout(rect=[0, 0.03, 1, 0.97]) # Adjust layout to prevent title overlap
    plt.show()

def main():
    train_dataset, train_response, _ = load_data(DATA_PATH)

    if train_dataset is None or train_response is None:
        return

    print("\nAnalyzing representative samples...")
    # Find the first index for each load level
    indices_to_plot = {}
    for load_g in LOAD_LEVELS_G:
        try:
            idx = np.where(train_response == load_g)[0][0]
            indices_to_plot[load_g] = idx
            print(f"Found sample index {idx} for load {load_g}g")
        except IndexError:
            print(f"Warning: No sample found for load {load_g}g in train_response")

    # Plot spectrogram for the first channel of the first sample of each load
    for load_g, sample_idx in indices_to_plot.items():
        channel_idx = 0 # Analyze the first channel for now
        print(f"\nPlotting spectrogram for Sample {sample_idx} (Load: {load_g}g, Channel: {channel_idx+1})...")
        current_signal = train_dataset[sample_idx, channel_idx, :]
        plot_spectrogram(current_signal, FS, load_g, channel_idx, sample_idx)

if __name__ == "__main__":
    main()
