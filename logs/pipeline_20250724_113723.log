2025-07-24 11:37:23,079 - INFO - ======================================================================
2025-07-24 11:37:23,079 - INFO - FINÁLNÍ OPTIMALIZOVANÝ PIPELINE - BALL BEARING CHALLENGE 2025
2025-07-24 11:37:23,079 - INFO - ======================================================================
2025-07-24 11:37:23,079 - INFO - Hardware: 96 CPU cores, 1007.1GB RAM
2025-07-24 11:37:23,079 - INFO - GPU: 2 GPUs, 90.0GB VRAM
2025-07-24 11:37:23,080 - INFO - Paralelizace: 32 workers
2025-07-24 11:37:23,080 - INFO - Načít<PERSON><PERSON> dat...
2025-07-24 11:37:23,488 - INFO - <PERSON> načtena. Trénovací: (6416, 4, 5000), Test: (3200, 4, 5000)
2025-07-24 11:37:23,488 - INFO - Extrakce příznaků...
2025-07-24 11:37:24,895 - INFO - Načítám příznaky z cache: cache/train_features_241b14a1f2a5e21b.npy
2025-07-24 11:37:24,899 - INFO - Načteny příznaky z cache: (6416, 131)
2025-07-24 11:37:25,598 - INFO - Načítám příznaky z cache: cache/test_features_03f2eab8e0d3024c.npy
2025-07-24 11:37:25,600 - INFO - Načteny příznaky z cache: (3200, 131)
2025-07-24 11:37:25,600 - INFO - Dimenze příznaků: 131 příznaků na vzorek
2025-07-24 11:37:25,601 - INFO - Aplikuji SMOTE-R augmentaci...
2025-07-24 11:37:25,622 - INFO - Augmentace dokončena: 6416 -> 8001 vzorků
2025-07-24 11:37:25,622 - INFO - Cross-validace finálního pipeline...
2025-07-24 11:37:25,624 - INFO - Používám 32 CPU cores pro paralelizaci
2025-07-24 11:37:25,624 - INFO - Pipeline vytvořen s XGBoost a hardware optimalizací
2025-07-24 11:37:38,850 - INFO - Cross-validace MAE: 1842.76 ± 40.80 g
2025-07-24 11:37:38,851 - INFO - Trénování finálního modelu na všech datech...
2025-07-24 11:37:40,980 - ERROR - Chyba při trénování: [11:37:40] /workspace/src/data/array_interface.cu:44: Check failed: err == cudaGetLastError() (0 vs. 2) : 
Stack trace:
  [bt] (0) /home/<USER>/Loziska/venv/lib64/python3.9/site-packages/xgboost/lib/libxgboost.so(+0x25c1ac) [0x7f7b56b901ac]
  [bt] (1) /home/<USER>/Loziska/venv/lib64/python3.9/site-packages/xgboost/lib/libxgboost.so(+0xa3f4bc) [0x7f7b573734bc]
  [bt] (2) /home/<USER>/Loziska/venv/lib64/python3.9/site-packages/xgboost/lib/libxgboost.so(+0x4e3a2e) [0x7f7b56e17a2e]
  [bt] (3) /home/<USER>/Loziska/venv/lib64/python3.9/site-packages/xgboost/lib/libxgboost.so(XGDMatrixSetInfoFromInterface+0xb2) [0x7f7b56a97522]
  [bt] (4) /lib64/libffi.so.8(+0x78d6) [0x7f7ca19418d6]
  [bt] (5) /lib64/libffi.so.8(+0x4556) [0x7f7ca193e556]
  [bt] (6) /usr/lib64/python3.9/lib-dynload/_ctypes.cpython-39-x86_64-linux-gnu.so(+0x91e9) [0x7f7bf353a1e9]
  [bt] (7) /usr/lib64/python3.9/lib-dynload/_ctypes.cpython-39-x86_64-linux-gnu.so(+0x89ba) [0x7f7bf35399ba]
  [bt] (8) /lib64/libpython3.9.so.1.0(_PyObject_MakeTpCall+0x2a3) [0x7f7ca1f39e73]


