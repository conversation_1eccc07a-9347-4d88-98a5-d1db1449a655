2025-07-24 11:38:35,284 - INFO - ======================================================================
2025-07-24 11:38:35,285 - INFO - FINÁLNÍ OPTIMALIZOVANÝ PIPELINE - BALL BEARING CHALLENGE 2025
2025-07-24 11:38:35,285 - INFO - ======================================================================
2025-07-24 11:38:35,285 - INFO - Hardware: 96 CPU cores, 1007.1GB RAM
2025-07-24 11:38:35,285 - INFO - GPU: 2 GPUs, 90.0GB VRAM
2025-07-24 11:38:35,285 - INFO - <PERSON>lelizace: 32 workers
2025-07-24 11:38:35,286 - INFO - Na<PERSON><PERSON><PERSON><PERSON><PERSON> dat...
2025-07-24 11:38:35,643 - INFO - Data úspěšně načtena. Trénovací: (6416, 4, 5000), Test: (3200, 4, 5000)
2025-07-24 11:38:35,643 - INFO - Extrakce příznaků...
2025-07-24 11:38:37,045 - INFO - Načítám příznaky z cache: cache/train_features_241b14a1f2a5e21b.npy
2025-07-24 11:38:37,048 - INFO - Načteny příznaky z cache: (6416, 131)
2025-07-24 11:38:37,751 - INFO - Načítám příznaky z cache: cache/test_features_03f2eab8e0d3024c.npy
2025-07-24 11:38:37,754 - INFO - Načteny příznaky z cache: (3200, 131)
2025-07-24 11:38:37,754 - INFO - Dimenze příznaků: 131 příznaků na vzorek
2025-07-24 11:38:37,754 - INFO - Aplikuji SMOTE-R augmentaci...
2025-07-24 11:38:37,775 - INFO - Augmentace dokončena: 6416 -> 8001 vzorků
2025-07-24 11:38:37,775 - INFO - Cross-validace finálního pipeline...
2025-07-24 11:38:37,776 - INFO - Používám 32 CPU cores pro paralelizaci
2025-07-24 11:38:37,777 - INFO - Pipeline vytvořen s XGBoost a hardware optimalizací
2025-07-24 11:38:51,053 - INFO - Cross-validace MAE: 1849.08 ± 28.84 g
2025-07-24 11:38:51,053 - INFO - Trénování finálního modelu na všech datech...
2025-07-24 11:38:53,226 - ERROR - Chyba při trénování: [11:38:52] /workspace/src/data/array_interface.cu:44: Check failed: err == cudaGetLastError() (0 vs. 2) : 
Stack trace:
  [bt] (0) /home/<USER>/Loziska/venv/lib64/python3.9/site-packages/xgboost/lib/libxgboost.so(+0x25c1ac) [0x7f284e7ef1ac]
  [bt] (1) /home/<USER>/Loziska/venv/lib64/python3.9/site-packages/xgboost/lib/libxgboost.so(+0xa3f4bc) [0x7f284efd24bc]
  [bt] (2) /home/<USER>/Loziska/venv/lib64/python3.9/site-packages/xgboost/lib/libxgboost.so(+0x4e3a2e) [0x7f284ea76a2e]
  [bt] (3) /home/<USER>/Loziska/venv/lib64/python3.9/site-packages/xgboost/lib/libxgboost.so(XGDMatrixSetInfoFromInterface+0xb2) [0x7f284e6f6522]
  [bt] (4) /lib64/libffi.so.8(+0x78d6) [0x7f29999ac8d6]
  [bt] (5) /lib64/libffi.so.8(+0x4556) [0x7f29999a9556]
  [bt] (6) /usr/lib64/python3.9/lib-dynload/_ctypes.cpython-39-x86_64-linux-gnu.so(+0x91e9) [0x7f28eb17a1e9]
  [bt] (7) /usr/lib64/python3.9/lib-dynload/_ctypes.cpython-39-x86_64-linux-gnu.so(+0x89ba) [0x7f28eb1799ba]
  [bt] (8) /lib64/libpython3.9.so.1.0(_PyObject_MakeTpCall+0x2a3) [0x7f2999b39e73]


