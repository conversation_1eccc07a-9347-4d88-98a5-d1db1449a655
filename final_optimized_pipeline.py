"""
Finální optimalizovaný pipeline kombinující nejlepší přístupy
pro Ball Bearing Predictive Challenge 2025.

Vylepšení v této verzi:
- Zohlednění nekonstantního (ne-stacionárního) spektra signálů
- <PERSON>kce č<PERSON>ově-proměnlivých statistik pomocí windowingu (chunking)
- Extrakce příznaků ze spektrogramu (STFT) pro zachycení dynamiky
- Vylepšená a agresivnější augmentace dat (SMOTE-R)
- Kalibrace predikcí s lineární extrapolací pro lepší pokrytí celého rozsahu
- Optimalizované a vyladěné hyperparametry pro XGBoost s early stopping
- Správné signal handling pro graceful shutdown
- Inteligentní data loading s cachováním
- Optimalizace pro dostupný hardware (96 CPU cores)
- Pokročilé logování a progress tracking
"""

import numpy as np
import pandas as pd
import pickle
import os
import sys
import signal
import time
import logging
import psutil
import hashlib
from pathlib import Path
from datetime import datetime
from tqdm import tqdm
from scipy.stats import skew, kurtosis
from scipy.signal import welch, find_peaks, stft
import pywt

# Import modelů a nástrojů
from sklearn.model_selection import KFold, cross_val_score, train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.pipeline import Pipeline
from sklearn.linear_model import Ridge
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, VotingRegressor
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF, Matern, WhiteKernel
from sklearn.isotonic import IsotonicRegression
from sklearn.metrics import make_scorer, mean_absolute_error
from sklearn.base import BaseEstimator, TransformerMixin

import xgboost as xgb
import lightgbm as lgb
import joblib
import warnings
warnings.filterwarnings('ignore')

# Globální proměnné pro signal handling
shutdown_requested = False
current_process = None

def setup_logging():
    """Nastavení pokročilého logování."""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"pipeline_{timestamp}.log"

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def signal_handler(signum, frame):
    """Handler pro graceful shutdown při Ctrl+C."""
    global shutdown_requested, current_process
    shutdown_requested = True
    logger = logging.getLogger(__name__)
    logger.warning(f"Přijat signal {signum}. Zahajuji graceful shutdown...")

    if current_process:
        logger.info("Ukončuji aktuální proces...")
        try:
            current_process.terminate()
        except:
            pass

    logger.info("Cleanup dokončen. Ukončuji program.")
    sys.exit(0)

def setup_signal_handlers():
    """Nastavení signal handlerů."""
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def get_hardware_info():
    """Získá informace o dostupném hardware."""
    info = {
        'cpu_count': psutil.cpu_count(logical=True),
        'memory_gb': psutil.virtual_memory().total / (1024**3),
        'gpu_count': 0,
        'gpu_memory_gb': 0
    }

    try:
        import GPUtil
        gpus = GPUtil.getGPUs()
        info['gpu_count'] = len(gpus)
        if gpus:
            info['gpu_memory_gb'] = sum(gpu.memoryTotal for gpu in gpus) / 1024
    except ImportError:
        # Fallback na nvidia-smi
        try:
            import subprocess
            result = subprocess.run(['nvidia-smi', '--query-gpu=count,memory.total',
                                   '--format=csv,noheader,nounits'],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                info['gpu_count'] = len(lines)
                info['gpu_memory_gb'] = sum(int(line.split(',')[1]) for line in lines) / 1024
        except:
            pass

    return info

class Config:
    """Konfigurace pipeline."""
    def __init__(self):
        # Cesty k souborům
        self.data_path = 'AMSM_LOZISKA_2025/student_data.pkl'
        self.cache_dir = Path('cache')
        self.results_dir = Path('results')

        # Hardware optimalizace
        hw_info = get_hardware_info()
        self.n_jobs = max(1, hw_info['cpu_count'] - 4)  # Ponechat 4 cores pro systém
        self.use_gpu = hw_info['gpu_count'] > 0

        # Data processing
        self.batch_size = 512  # Menší batch pro lepší memory management
        self.augment_factor = 5  # Agresivnější augmentace
        self.noise_std = 50

        # Model parametry
        self.cv_folds = 5
        self.random_state = 42

        # Memory management
        self.max_memory_usage = 0.8  # 80% max memory usage

        # Vytvoření adresářů
        self.cache_dir.mkdir(exist_ok=True)
        self.results_dir.mkdir(exist_ok=True)

print("Všechny knihovny úspěšně naimportovány.")


class DataCache:
    """Správa cache pro extrahované příznaky."""

    def __init__(self, cache_dir):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)

    def _get_hash(self, data):
        """Vytvoří hash z dat pro identifikaci cache."""
        if isinstance(data, np.ndarray):
            return hashlib.md5(data.tobytes()).hexdigest()
        else:
            return hashlib.md5(str(data).encode()).hexdigest()

    def load(self, data, name):
        """Načte data z cache pokud existují."""
        path = self.cache_dir / f"{name}_{self._get_hash(data)}.pkl"
        if path.exists():
            self.logger.info(f"Načítám z cache '{name}': {path}")
            return joblib.load(path)
        return None

    def save(self, obj, data, name):
        """Uloží data do cache."""
        path = self.cache_dir / f"{name}_{self._get_hash(data)}.pkl"
        joblib.dump(obj, path)
        self.logger.info(f"Uloženo do cache '{name}': {path}")

    # Zachování kompatibility se starým rozhraním
    def load_features(self, data, feature_type="features"):
        return self.load(data, feature_type)

    def save_features(self, features, data, feature_type="features"):
        return self.save(features, data, feature_type)

class MemoryMonitor:
    """Monitoring paměti během zpracování."""

    def __init__(self, max_usage=0.8):
        self.max_usage = max_usage
        self.logger = logging.getLogger(__name__)

    def check_memory(self):
        """Zkontroluje aktuální využití paměti."""
        memory = psutil.virtual_memory()
        usage = memory.percent / 100

        if usage > self.max_usage:
            self.logger.warning(f"Vysoké využití paměti: {usage:.1%}")
            return False
        return True

    def force_gc(self):
        """Vynutí garbage collection."""
        import gc
        gc.collect()
        self.logger.info("Garbage collection dokončen")

class SmoteRTransformer(BaseEstimator, TransformerMixin):
    """SMOTE pro regresi - vylepšená agresivnější augmentace dat mezi diskrétními úrovněmi."""

    def __init__(self, discrete_levels=None, augment_factor=5, noise_std=50):
        self.discrete_levels = discrete_levels or [2500, 7500, 12500, 17500]
        self.augment_factor = augment_factor
        self.noise_std = noise_std
        self.logger = logging.getLogger(__name__)

    def fit(self, X, y=None):
        return self

    def transform(self, X):
        return X

    def fit_resample(self, X, y):
        """Vytvoří syntetické vzorky mezi diskrétními úrovněmi s agresivnější strategií."""
        global shutdown_requested

        X_aug = [X]
        y_aug = [y]

        self.logger.info(f"Aplikuji SMOTE-R augmentaci (faktor: {self.augment_factor})...")

        for round_idx in tqdm(range(self.augment_factor), desc="Augmentace"):
            if shutdown_requested:
                self.logger.warning("Shutdown požadován, ukončuji augmentaci")
                break

            X_new_gen = []
            y_new_gen = []

            # Aktuální pool dat (včetně předchozích augmentací)
            current_y_pool = np.concatenate(y_aug)
            current_X_pool = np.vstack(X_aug)

            for i in range(len(self.discrete_levels) - 1):
                if shutdown_requested:
                    break

                level1, level2 = self.discrete_levels[i], self.discrete_levels[i+1]

                # Širší tolerance pro výběr vzorků
                idx1 = np.where(np.abs(current_y_pool - level1) < 500)[0]
                idx2 = np.where(np.abs(current_y_pool - level2) < 500)[0]

                if len(idx1) == 0 or len(idx2) == 0:
                    continue

                n_synthetic = min(len(idx1), len(idx2))
                pairs1 = np.random.choice(idx1, size=n_synthetic, replace=True)
                pairs2 = np.random.choice(idx2, size=n_synthetic, replace=True)

                for i1, i2 in zip(pairs1, pairs2):
                    if shutdown_requested:
                        break

                    # Variabilnější interpolace s normálním rozložením kolem 0.5
                    alpha = np.random.normal(0.5, 0.15)
                    alpha = np.clip(alpha, 0.05, 0.95)

                    # Syntetický vzorek
                    X_synth = (1 - alpha) * current_X_pool[i1] + alpha * current_X_pool[i2]
                    y_synth = (1 - alpha) * current_y_pool[i1] + alpha * current_y_pool[i2]
                    y_synth += np.random.normal(0, self.noise_std)

                    X_new_gen.append(X_synth)
                    y_new_gen.append(y_synth)

            if X_new_gen:
                X_aug.append(np.array(X_new_gen))
                y_aug.append(np.array(y_new_gen))

        X_final = np.vstack(X_aug)
        y_final = np.concatenate(y_aug)
        self.logger.info(f"Augmentace dokončena: {len(X)} -> {len(X_final)} vzorků")

        return X_final, y_final


class CalibratedPredictor:
    """Kalibrátor s lineární extrapolací pro lepší pokrytí celého rozsahu."""

    def __init__(self, pipeline, isotonic):
        self.pipeline = pipeline
        self.isotonic = isotonic
        self.logger = logging.getLogger(__name__)

        # Hranice isotonic regression pro extrapolaci
        self.min_pred = np.min(isotonic.X_thresholds_)
        self.max_pred = np.max(isotonic.X_thresholds_)
        self.min_val = np.min(isotonic.y_thresholds_)
        self.max_val = np.max(isotonic.y_thresholds_)

        self.logger.info(f"Kalibrátor: rozsah [{self.min_pred:.1f}, {self.max_pred:.1f}] -> [{self.min_val:.1f}, {self.max_val:.1f}]")

    def predict(self, X):
        """Predikce s kalibrací a lineární extrapolací."""
        raw_pred = self.pipeline.predict(X)
        calibrated_pred = self.isotonic.predict(raw_pred)

        # Lineární extrapolace mimo rozsah isotonic regression
        extrapolated = np.copy(calibrated_pred)

        # Extrapolace pod minimem
        mask_low = raw_pred < self.min_pred
        if np.any(mask_low):
            extrapolated[mask_low] = self.min_val + (raw_pred[mask_low] - self.min_pred)

        # Extrapolace nad maximem
        mask_high = raw_pred > self.max_pred
        if np.any(mask_high):
            extrapolated[mask_high] = self.max_val + (raw_pred[mask_high] - self.max_pred)

        # Clipping do povoleného rozsahu
        return np.clip(extrapolated, 0, 19200)


def extract_features_final(sample, fs=1.5625e6, n_chunks=10):
    """
    Finální extrakce příznaků s časově-proměnlivými statistikami a spektrogramem.
    Klíčové vylepšení: zachycení ne-stacionárního spektra signálů.
    """
    all_features = []

    for channel_data in sample:
        # === 1. Globální časové příznaky ===
        rms = np.sqrt(np.mean(channel_data**2))
        all_features.extend([
            np.mean(channel_data),
            np.std(channel_data),
            skew(channel_data),
            kurtosis(channel_data),
            np.ptp(channel_data),  # Peak-to-peak
            rms,
            np.max(np.abs(channel_data)) / (rms + 1e-9)  # Crest Factor
        ])

        # === 2. Časově-proměnlivé statistiky (Chunking) ===
        # Rozdělení signálu na n_chunks částí pro zachycení dynamiky
        chunks = np.array_split(channel_data, n_chunks)
        chunk_means = [np.mean(c) for c in chunks]
        chunk_stds = [np.std(c) for c in chunks]
        chunk_kurtosis = [kurtosis(c) for c in chunks]

        # Statistiky změn v čase - klíčové pro ne-stacionární signály
        all_features.extend([
            np.mean(chunk_stds),      # Průměrná variabilita
            np.std(chunk_stds),       # Jak se mění rozptyl v čase
            np.mean(chunk_kurtosis),  # Průměrná špičatost
            np.std(chunk_kurtosis)    # Jak se mění špičatost v čase
        ])

        # === 3. Příznaky ze Spektrogramu (STFT) ===
        # Zachycení časově-frekvenční dynamiky
        f, t, Zxx = stft(channel_data, fs=fs, nperseg=256, noverlap=128)
        Sxx = np.abs(Zxx)

        # Statistiky celého spektrogramu
        all_features.extend([
            np.mean(Sxx),
            np.std(Sxx),
            skew(Sxx.flatten()),
            kurtosis(Sxx.flatten())
        ])

        # Dynamika energie v čase (změna sloupců spektrogramu)
        energy_envelope = np.sum(Sxx, axis=0)
        all_features.append(np.std(energy_envelope))  # Variabilita energie v čase

        # Průměrné spektrum (zprůměrování přes čas)
        mean_spectrum = np.mean(Sxx, axis=1)
        if np.sum(mean_spectrum) > 0:
            spectral_centroid = np.sum(f * mean_spectrum) / np.sum(mean_spectrum)
        else:
            spectral_centroid = 0
        all_features.append(spectral_centroid)

        # === 4. Příznaky z Waveletů (časově-frekvenční dekompozice) ===
        # Waveletová dekompozice pro různé frekvenční pásma
        coeffs = pywt.wavedec(channel_data, 'db4', level=6)
        for level_coeffs in coeffs:
            all_features.extend([
                np.mean(np.abs(level_coeffs)),  # Průměrná energie v pásmu
                np.std(level_coeffs),           # Variabilita v pásmu
                np.sum(level_coeffs**2)         # Celková energie v pásmu
            ])

    # === 5. Mezi-kanálové příznaky ===
    # Korelace mezi kanály - důležité pro prostorovou lokalizaci
    corr_matrix = np.corrcoef(sample)
    all_features.extend(corr_matrix[np.triu_indices(4, k=1)])

    return np.array(all_features)

def process_in_batches(data, func, cache, name, batch_size=512):
    """
    Zpracování dat v dávkách s cachováním.
    """
    global shutdown_requested
    logger = logging.getLogger(__name__)

    # Zkusit načíst z cache
    cached_data = cache.load(data, name)
    if cached_data is not None:
        logger.info(f"Načteno z cache '{name}': {cached_data.shape}")
        return cached_data

    logger.info(f"Zpracovávám '{name}' v dávkách...")

    # Zpracování s progress barem
    results = []
    for sample in tqdm(data, desc=name):
        if shutdown_requested:
            logger.warning("Shutdown požadován, ukončuji zpracování")
            break
        results.append(func(sample))

    if not results:
        logger.error("Žádná data nebyla zpracována")
        return None

    results = np.array(results)

    # Uložit do cache
    cache.save(results, data, name)

    logger.info(f"Zpracování '{name}' dokončeno: {results.shape}")
    return results


# WeightedVotingRegressor odstraněn pro jednoduchost a rychlost


def create_final_pipeline(config, use_early_stopping=False):
    """Vytvoří finální pipeline s optimalizovanými hyperparametry."""
    logger = logging.getLogger(__name__)

    # Hardware-optimalizované parametry
    n_jobs = config.n_jobs
    logger.info(f"Používám {n_jobs} CPU cores pro paralelizaci")

    # Vylepšené hyperparametry pro XGBoost
    model_params = {
        'n_estimators': 2000,        # Více estimátorů pro lepší výkon
        'learning_rate': 0.02,       # Nižší learning rate pro jemnější učení
        'max_depth': 10,             # Hlubší stromy pro komplexnější vzory
        'subsample': 0.7,            # Agresivnější subsampling
        'colsample_bytree': 0.7,     # Agresivnější feature sampling
        'gamma': 0.05,               # Nižší gamma pro méně agresivní pruning
        'reg_alpha': 0.05,           # L1 regularizace
        'reg_lambda': 1.0,           # L2 regularizace
        'random_state': config.random_state,
        'n_jobs': n_jobs,
        'tree_method': 'hist',       # Rychlejší pro velké datasety
        'verbosity': 0
    }

    # Přidání early stopping parametrů pokud je požadováno
    if use_early_stopping:
        model_params.update({
            'early_stopping_rounds': 50,
            'eval_metric': 'mae'
        })

    model = xgb.XGBRegressor(**model_params)

    # Kompletní pipeline
    pipeline = Pipeline([
        ('scaler', RobustScaler()),  # Robustnější vůči outlierům
        ('model', model)
    ])

    logger.info(f"Pipeline vytvořen s XGBoost (early_stopping={use_early_stopping})")
    return pipeline


def main():
    """Hlavní funkce orchestrující celý proces."""
    global current_process, shutdown_requested

    # Nastavení signal handlers a logování
    setup_signal_handlers()
    logger = setup_logging()

    # Konfigurace
    config = Config()

    # Hardware info
    hw_info = get_hardware_info()
    logger.info("="*70)
    logger.info("FINÁLNÍ OPTIMALIZOVANÝ PIPELINE - BALL BEARING CHALLENGE 2025")
    logger.info("="*70)
    logger.info(f"Hardware: {hw_info['cpu_count']} CPU cores, {hw_info['memory_gb']:.1f}GB RAM")
    logger.info(f"GPU: {hw_info['gpu_count']} GPUs, {hw_info['gpu_memory_gb']:.1f}GB VRAM")
    logger.info(f"Paralelizace: {config.n_jobs} workers")

    # Inicializace cache a memory monitor
    cache = DataCache(config.cache_dir)
    memory_monitor = MemoryMonitor(config.max_memory_usage)

    # Načtení dat
    logger.info("Načítání dat...")
    try:
        with open(config.data_path, 'rb') as f:
            data = pickle.load(f)
        X_train_raw = data['train_dataset']
        y_train = data['train_response']
        X_test_raw = data['test_dataset']
        logger.info(f"Data úspěšně načtena. Trénovací: {X_train_raw.shape}, Test: {X_test_raw.shape}")
    except FileNotFoundError:
        logger.error(f"Chyba: Soubor '{config.data_path}' nebyl nalezen.")
        return
    except Exception as e:
        logger.error(f"Chyba při načítání dat: {e}")
        return

    if shutdown_requested:
        return

    # Extrakce příznaků s cachováním - použití nové finální funkce
    logger.info("Extrakce příznaků...")
    X_train = process_in_batches(X_train_raw, extract_features_final, cache, "train_features_v2", config.batch_size)
    if X_train is None or shutdown_requested:
        return

    X_test = process_in_batches(X_test_raw, extract_features_final, cache, "test_features_v2", config.batch_size)
    if X_test is None or shutdown_requested:
        return

    # Ošetření NaN a Inf hodnot
    if np.any(np.isnan(X_train)) or np.any(np.isinf(X_train)):
        logger.warning("Nalezeny NaN/Inf hodnoty v trénovacích datech, opravuji...")
        X_train = np.nan_to_num(X_train)

    if np.any(np.isnan(X_test)) or np.any(np.isinf(X_test)):
        logger.warning("Nalezeny NaN/Inf hodnoty v testovacích datech, opravuji...")
        X_test = np.nan_to_num(X_test)

    logger.info(f"Dimenze příznaků: {X_train.shape[1]} příznaků na vzorek")

    # Kontrola paměti před augmentací
    if not memory_monitor.check_memory():
        memory_monitor.force_gc()

    # Data augmentation
    if not shutdown_requested:
        smoter = SmoteRTransformer(
            augment_factor=config.augment_factor,
            noise_std=config.noise_std
        )
        X_train_aug, y_train_aug = smoter.fit_resample(X_train, y_train)

        if shutdown_requested:
            return
    else:
        return

    # Cross-validace na augmentovaných datech
    logger.info("Cross-validace finálního pipeline...")
    cv_splitter = KFold(n_splits=config.cv_folds, shuffle=True, random_state=config.random_state)

    # Pipeline pro cross-validaci (bez early stopping)
    pipeline_cv = create_final_pipeline(config, use_early_stopping=False)

    if not shutdown_requested:
        try:
            cv_scores = cross_val_score(
                pipeline_cv, X_train_aug, y_train_aug,
                cv=cv_splitter, scoring='neg_mean_absolute_error', n_jobs=config.n_jobs
            )

            cv_mae = -np.mean(cv_scores)
            cv_std = np.std(cv_scores)
            logger.info(f"Cross-validace MAE: {cv_mae:.2f} ± {cv_std:.2f} g")
        except Exception as e:
            logger.error(f"Chyba při cross-validaci: {e}")
            return
    else:
        return

    # Trénování finálního modelu s early stopping
    if not shutdown_requested:
        logger.info("Trénování finálního modelu s early stopping...")

        # Rozdělení pro early stopping
        X_train_final, X_val, y_train_final, y_val = train_test_split(
            X_train_aug, y_train_aug, test_size=0.1, random_state=config.random_state
        )

        # Pipeline s early stopping
        pipeline_final = create_final_pipeline(config, use_early_stopping=True)

        try:
            start_time = time.time()

            # Fit scaler na trénovacích datech
            scaler = pipeline_final.named_steps['scaler']
            X_train_scaled = scaler.fit_transform(X_train_final)
            X_val_scaled = scaler.transform(X_val)

            # Trénování s early stopping
            pipeline_final.named_steps['model'].fit(
                X_train_scaled, y_train_final,
                eval_set=[(X_val_scaled, y_val)],
                verbose=False
            )

            training_time = time.time() - start_time
            best_iteration = pipeline_final.named_steps['model'].best_iteration
            logger.info(f"Trénování dokončeno za {training_time:.1f}s")
            logger.info(f"Nejlepší iterace (Early Stopping): {best_iteration}")

        except Exception as e:
            logger.error(f"Chyba při trénování: {e}")
            return
    else:
        return

    # Kalibrace s vylepšeným CalibratedPredictor
    if not shutdown_requested:
        logger.info("Kalibrace pomocí Isotonic Regression s extrapolací...")
        try:
            train_pred = pipeline_final.predict(X_train)
            isotonic = IsotonicRegression(out_of_bounds='clip', y_min=0, y_max=19200)
            isotonic.fit(train_pred, y_train)

            # Vytvoření kalibrovaného prediktoru
            final_predictor = CalibratedPredictor(pipeline_final, isotonic)

        except Exception as e:
            logger.error(f"Chyba při kalibraci: {e}")
            return
    else:
        return

    # Finální predikce s kalibrací a extrapolací
    if not shutdown_requested:
        logger.info("Vytváření finálních predikcí...")
        try:
            final_predictions = final_predictor.predict(X_test)
        except Exception as e:
            logger.error(f"Chyba při predikci: {e}")
            return
    else:
        return

    # Analýza predikcí
    logger.info("Analýza finálních predikcí...")
    logger.info(f"Rozsah: {final_predictions.min():.2f} - {final_predictions.max():.2f} g")
    logger.info(f"Průměr: {final_predictions.mean():.2f} g")
    logger.info(f"Std: {final_predictions.std():.2f} g")

    # Distribuce kolem trénovacích úrovní
    train_levels = [2500, 7500, 12500, 17500]
    logger.info("Distribuce predikcí kolem trénovacích úrovní:")
    for level in train_levels:
        close = np.sum(np.abs(final_predictions - level) < 500)
        logger.info(f"  ±500g od {level}g: {close} predikcí ({close/len(final_predictions)*100:.1f}%)")

    # Uložení výsledků
    logger.info("Ukládání výsledků...")
    try:
        # Uložení do results adresáře
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = config.results_dir / timestamp
        results_dir.mkdir(exist_ok=True)

        np.save(results_dir / 'predictions_final.npy', final_predictions)
        joblib.dump(final_predictor, results_dir / 'final_predictor.pkl')

        # Také uložit do hlavního adresáře pro kompatibilitu
        np.save('predictions_final.npy', final_predictions)
        joblib.dump(final_predictor, 'final_predictor.pkl')

        logger.info(f"Výsledky uloženy do: {results_dir}")
    except Exception as e:
        logger.error(f"Chyba při ukládání: {e}")

    # Ukázka predikcí (prvních 5 vzorků)
    try:
        logger.info("Ukázka predikcí (prvních 5 vzorků):")
        sample_predictions = final_predictions[:5]
        for i, pred in enumerate(sample_predictions):
            logger.info(f"  Vzorek {i+1}: {pred:.2f} g")
    except Exception as e:
        logger.warning(f"Chyba při zobrazení ukázky: {e}")

    # Finální statistiky
    final_memory = psutil.virtual_memory()
    logger.info("="*70)
    logger.info("PIPELINE ÚSPĚŠNĚ DOKONČEN!")
    logger.info("="*70)
    logger.info(f"Finální využití paměti: {final_memory.percent:.1f}%")
    logger.info("Soubory:")
    logger.info("  - predictions_final.npy : Finální predikce")
    logger.info("  - final_predictor.pkl : Kalibrovaný prediktor")
    logger.info(f"  - logs/pipeline_final_*.log : Log soubor")


if __name__ == "__main__":
    main()